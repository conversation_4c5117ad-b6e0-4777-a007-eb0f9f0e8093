<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الباركود المخصص - الوضع المتجاوب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .barcode-input {
            font-size: 18px;
            padding: 10px;
            margin: 10px 0;
        }
        .test-results {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .item-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .highlight {
            background-color: #fff3cd !important;
        }
        #selectedItemsTable {
            width: 100%;
            margin-top: 20px;
        }
        #selectedItemsTable th,
        #selectedItemsTable td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .quantity-input {
            width: 60px !important;
            text-align: center;
        }
        .calculation-log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">اختبار إصلاح الباركود المخصص - الوضع المتجاوب</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>قائمة الأصناف</h5>
                    </div>
                    <div class="card-body">
                        <input type="text" id="barcodeInput" class="form-control barcode-input" 
                               placeholder="امسح الباركود أو أدخله يدوياً" 
                               autocomplete="off">
                        
                        <table id="accountsTable" class="table table-striped mt-3">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr data-item-id="1" data-item-name="جبنة تركي" data-item-price="280.00" data-item-barcode="000001">
                                    <td>جبنة تركي</td>
                                    <td>280.00/كيلو</td>
                                    <td class="add-cell">
                                        <button class="btn btn-primary btn-sm add-item-btn">إضافة</button>
                                    </td>
                                </tr>
                                <tr data-item-id="2" data-item-name="لحم بقري" data-item-price="450.00" data-item-barcode="000002">
                                    <td>لحم بقري</td>
                                    <td>450.00/كيلو</td>
                                    <td class="add-cell">
                                        <button class="btn btn-primary btn-sm add-item-btn">إضافة</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>الأصناف المختارة</h5>
                    </div>
                    <div class="card-body">
                        <table id="selectedItemsTable" class="table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        
                        <div class="mt-3">
                            <strong>إجمالي الفاتورة: <span id="invoiceTotal">0.00</span> جنيه</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h5>سجل العمليات والحسابات</h5>
            <div id="calculationLog" class="calculation-log"></div>
            <button class="btn btn-secondary btn-sm" onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div class="mt-3">
            <h6>اختبارات سريعة:</h6>
            <button class="btn btn-info btn-sm" onclick="testBarcode('2000001012500')">جبنة تركي - 1.250 كيلو</button>
            <button class="btn btn-info btn-sm" onclick="testBarcode('2000002020000')">لحم بقري - 2.000 كيلو</button>
            <button class="btn btn-warning btn-sm" onclick="clearAllItems()">مسح جميع الأصناف</button>
        </div>
    </div>

    <script>
        // متغيرات عامة
        window.addedItems = [];
        const barcodeItemsMap = {
            '000001': '1',
            '000002': '2'
        };

        // دالة تسجيل العمليات
        function log(message) {
            const logDiv = document.getElementById('calculationLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('calculationLog').innerHTML = '';
        }

        // دالة معالجة الباركود المخصص
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            log(`معالجة باركود مخصص: ${barcode} -> صنف: ${itemBarcode}, وزن: ${weightInGrams}جم, كمية: ${quantityInKg.toFixed(3)}كيلو`);

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        // البحث عن صنف بالمعرف
        function findItemById(itemId) {
            const row = document.querySelector(`#accountsTable tr[data-item-id="${itemId}"]`);
            if (row) {
                return {
                    id: itemId,
                    name: row.dataset.itemName,
                    price: row.dataset.itemPrice,
                    element: row
                };
            }
            return null;
        }

        // تحديث إجمالي الفاتورة
        function updateInvoiceTotal() {
            let total = 0;
            window.addedItems.forEach(item => {
                const itemTotal = parseFloat(item.quantity) * parseFloat(item.price);
                total += itemTotal;
            });
            document.getElementById('invoiceTotal').textContent = total.toFixed(2);
            log(`تحديث إجمالي الفاتورة: ${total.toFixed(2)} جنيه`);
        }

        function testBarcode(barcode) {
            document.getElementById('barcodeInput').value = barcode;
            document.getElementById('barcodeInput').dispatchEvent(new Event('input'));
        }

        function clearAllItems() {
            window.addedItems = [];
            document.getElementById('selectedItemsTable').querySelector('tbody').innerHTML = '';
            document.querySelectorAll('#accountsTable tr.highlight').forEach(row => {
                row.classList.remove('highlight');
                const addCell = row.querySelector('.add-cell');
                if (addCell) {
                    addCell.innerHTML = '<button class="btn btn-primary btn-sm add-item-btn">إضافة</button>';
                }
            });
            updateInvoiceTotal();
            log('تم مسح جميع الأصناف');
        }

        // تحميل الكود المحدث من invoice_responsive.js
        // (سيتم تضمين الدوال المحدثة هنا)
    </script>
    
    <!-- تضمين جزء من invoice_responsive.js للاختبار -->
    <script src="js/invoice_responsive.js"></script>
    
    <script>
        // إعداد معالج الباركود
        document.addEventListener('DOMContentLoaded', function() {
            const barcodeInput = document.getElementById('barcodeInput');
            
            barcodeInput.addEventListener('input', function() {
                const barcode = this.value.trim();
                
                if (barcode.length >= 13) {
                    log(`تم إدخال باركود: ${barcode}`);
                    
                    // معالجة الباركود المخصص
                    const customBarcodeData = processCustomBarcode(barcode);
                    
                    if (customBarcodeData) {
                        const itemId = barcodeItemsMap[customBarcodeData.itemBarcode];
                        if (itemId) {
                            const item = findItemById(itemId);
                            if (item) {
                                item.quantity = customBarcodeData.quantity;
                                item.isCustomBarcode = true;
                                item.customBarcodeData = customBarcodeData;
                                
                                // استخدام الدالة المحدثة
                                if (typeof addItemToSelectedList === 'function') {
                                    addItemToSelectedList(item);
                                } else {
                                    log('خطأ: دالة addItemToSelectedList غير موجودة');
                                }
                                
                                this.value = '';
                            }
                        } else {
                            log(`باركود صنف غير معروف: ${customBarcodeData.itemBarcode}`);
                        }
                    } else {
                        // باركود عادي
                        const itemId = barcodeItemsMap[barcode];
                        if (itemId) {
                            const item = findItemById(itemId);
                            if (item && typeof addItemToSelectedList === 'function') {
                                addItemToSelectedList(item);
                                this.value = '';
                            }
                        } else {
                            log(`باركود غير معروف: ${barcode}`);
                        }
                    }
                }
            });
            
            log('تم تحميل صفحة الاختبار بنجاح');
        });
    </script>
</body>
</html>
