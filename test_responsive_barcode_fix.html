<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الباركود المخصص - الوضع المتجاوب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .barcode-input {
            font-size: 18px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .test-results {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .item-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        
        .highlight {
            background-color: #fff3cd !important;
        }
        
        #selectedItemsTable {
            width: 100%;
            margin-top: 20px;
        }
        
        #selectedItemsTable th,
        #selectedItemsTable td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .quantity-input {
            width: 60px !important;
            text-align: center;
        }
        
        .calculation-log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">اختبار إصلاح الباركود المخصص - الوضع المتجاوب</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>قائمة الأصناف</h5>
                    </div>
                    <div class="card-body">
                        <input type="text" id="barcodeInput" class="form-control barcode-input" placeholder="امسح الباركود أو أدخله يدوياً" autocomplete="off">

                        <table id="accountsTable" class="table table-striped mt-3">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr data-item-id="1" data-item-name="جبنة تركي" data-item-price="280.00" data-item-barcode="000001">
                                    <td>جبنة تركي</td>
                                    <td>280.00/كيلو</td>
                                    <td class="add-cell">
                                        <button class="btn btn-primary btn-sm add-item-btn">إضافة</button>
                                    </td>
                                </tr>
                                <tr data-item-id="2" data-item-name="لحم بقري" data-item-price="450.00" data-item-barcode="000002">
                                    <td>لحم بقري</td>
                                    <td>450.00/كيلو</td>
                                    <td class="add-cell">
                                        <button class="btn btn-primary btn-sm add-item-btn">إضافة</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>الأصناف المختارة</h5>
                    </div>
                    <div class="card-body">
                        <table id="selectedItemsTable" class="table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>

                        <div class="mt-3">
                            <strong>إجمالي الفاتورة: <span id="invoiceTotal">0.00</span> جنيه</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-results">
            <h5>سجل العمليات والحسابات</h5>
            <div id="calculationLog" class="calculation-log"></div>
            <button class="btn btn-secondary btn-sm" onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="mt-3">
            <h6>اختبارات سريعة:</h6>
            <button class="btn btn-info btn-sm" onclick="testBarcode('2000001012500')">جبنة تركي - 1.250 كيلو</button>
            <button class="btn btn-info btn-sm" onclick="testBarcode('2000002020000')">لحم بقري - 2.000 كيلو</button>
            <button class="btn btn-warning btn-sm" onclick="clearAllItems()">مسح جميع الأصناف</button>
        </div>
    </div>

    <script>
        // متغيرات عامة
        window.addedItems = [];
        const barcodeItemsMap = {
            '000001': '1',
            '000002': '2'
        };

        // دالة تسجيل العمليات
        function log(message) {
            const logDiv = document.getElementById('calculationLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('calculationLog').innerHTML = '';
        }

        // دالة معالجة الباركود المخصص
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            log(`معالجة باركود مخصص: ${barcode} -> صنف: ${itemBarcode}, وزن: ${weightInGrams}جم, كمية: ${quantityInKg.toFixed(3)}كيلو`);

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        // البحث عن صنف بالمعرف
        function findItemById(itemId) {
            const row = document.querySelector(`#accountsTable tr[data-item-id="${itemId}"]`);
            if (row) {
                return {
                    id: itemId,
                    name: row.dataset.itemName,
                    price: row.dataset.itemPrice,
                    element: row
                };
            }
            return null;
        }

        // تحديث إجمالي الفاتورة
        function updateInvoiceTotal() {
            let total = 0;
            window.addedItems.forEach(item => {
                const itemTotal = parseFloat(item.quantity) * parseFloat(item.price);
                total += itemTotal;
            });
            document.getElementById('invoiceTotal').textContent = total.toFixed(2);
            log(`تحديث إجمالي الفاتورة: ${total.toFixed(2)} جنيه`);
        }

        function testBarcode(barcode) {
            document.getElementById('barcodeInput').value = barcode;
            document.getElementById('barcodeInput').dispatchEvent(new Event('input'));
        }

        function clearAllItems() {
            window.addedItems = [];
            document.getElementById('selectedItemsTable').querySelector('tbody').innerHTML = '';
            document.querySelectorAll('#accountsTable tr.highlight').forEach(row => {
                row.classList.remove('highlight');
                const addCell = row.querySelector('.add-cell');
                if (addCell) {
                    addCell.innerHTML = '<button class="btn btn-primary btn-sm add-item-btn">إضافة</button>';
                }
            });
            updateInvoiceTotal();
            log('تم مسح جميع الأصناف');
        }

        // تحميل الكود المحدث من invoice_responsive.js
        // (سيتم تضمين الدوال المحدثة هنا)
    </script>

    <script>
        // نسخة مبسطة من addItemToSelectedList مع الإصلاحات المطبقة
        function addItemToSelectedList(item) {
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            if (!selectedItemsTable) return;

            // تأكد من أن window.addedItems موجود
            if (!window.addedItems) {
                window.addedItems = [];
            }

            // تحقق مما إذا كان العنصر موجودًا بالفعل
            const existingItemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);

            if (existingItemIndex !== -1) {
                // العنصر موجود بالفعل، قم بزيادة الكمية
                const currentQuantity = parseFloat(window.addedItems[existingItemIndex].quantity) || 1;
                const newQuantity = item.quantity ? parseFloat(item.quantity) : (currentQuantity + 1);
                window.addedItems[existingItemIndex].quantity = newQuantity;

                // تحديث واجهة المستخدم
                const existingRow = Array.from(selectedItemsTable.querySelectorAll('tr')).find(
                    row => row.dataset.itemId === item.id
                );

                if (existingRow) {
                    const quantityInput = existingRow.querySelector('.quantity-input');
                    if (quantityInput) {
                        quantityInput.value = newQuantity;

                        // تحديث خلية الإجمالي
                        const price = parseFloat(existingRow.dataset.itemPrice) || 0;
                        const newTotal = newQuantity * price;

                        const totalCell = existingRow.querySelector('.item-total');
                        if (totalCell) {
                            totalCell.textContent = newTotal.toFixed(2);
                        }

                        log(`تحديث صنف موجود: ${item.name}, الكمية الجديدة: ${newQuantity}, الإجمالي: ${newTotal.toFixed(2)}`);
                    }
                }

                updateInvoiceTotal();
                return;
            }

            // الحصول على سعر العنصر
            const itemPrice = item.element ? item.element.dataset.itemPrice : (item.price || '0');

            // تحديد الكمية الصحيحة للإضافة (الإصلاح المطبق)
            let correctQuantity;
            if (item.isCustomBarcode && item.customBarcodeData && item.customBarcodeData.quantity) {
                correctQuantity = item.customBarcodeData.quantity;
                log(`إضافة صنف بباركود مخصص - الكمية من الباركود: ${correctQuantity}`);
            } else {
                correctQuantity = item.quantity || 1;
                log(`إضافة صنف عادي - الكمية: ${correctQuantity}`);
            }

            // إضافة العنصر إلى window.addedItems
            window.addedItems.push({
                id: item.id,
                name: item.name,
                quantity: correctQuantity,
                price: itemPrice,
                isCustomBarcode: item.isCustomBarcode || false,
                customBarcodeData: item.customBarcodeData || null
            });

            // إنشاء صف جديد للعنصر
            const tr = document.createElement('tr');
            tr.dataset.itemId = item.id;
            tr.dataset.itemName = item.name;
            tr.dataset.itemPrice = itemPrice;

            // حساب الإجمالي - استخدام الكمية الصحيحة
            const itemTotal = correctQuantity * parseFloat(itemPrice || 0);
            log(`إنشاء صف جديد - الصنف: ${item.name}, الكمية: ${correctQuantity}, السعر: ${itemPrice}, الإجمالي: ${itemTotal.toFixed(2)}`);

            tr.innerHTML = `
                <td style="text-align: right; padding: 8px;">${item.name}</td>
                <td style="text-align: center; padding: 8px;">${itemPrice}/كيلو</td>
                <td style="text-align: center; padding: 8px;">
                    <input type="number" class="form-control quantity-input" min="0.1" step="0.1" value="${correctQuantity}" style="width: 60px; margin: auto; text-align: center;">
                </td>
                <td class="item-total" style="text-align: center; padding: 8px; font-weight: bold;">${itemTotal.toFixed(2)}</td>
                <td style="text-align: center; padding: 8px;">
                    <button type="button" class="btn btn-danger btn-sm remove-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            // إضافة معالج حدث لزر الحذف
            const removeBtn = tr.querySelector('.remove-btn');
            removeBtn.addEventListener('click', function() {
                // إزالة من window.addedItems
                const itemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);
                if (itemIndex !== -1) {
                    window.addedItems.splice(itemIndex, 1);
                }

                // إزالة الصف
                tr.remove();

                // إزالة التمييز من الجدول الأصلي
                const originalRow = document.querySelector(`#accountsTable tr[data-item-id="${item.id}"]`);
                if (originalRow) {
                    originalRow.classList.remove('highlight');
                    const addCell = originalRow.querySelector('.add-cell');
                    if (addCell) {
                        addCell.innerHTML = '<button class="btn btn-primary btn-sm add-item-btn">إضافة</button>';
                    }
                }

                updateInvoiceTotal();
                log(`تم حذف الصنف: ${item.name}`);
            });

            // معالجة تغيير الكمية
            const quantityInput = tr.querySelector('.quantity-input');
            quantityInput.addEventListener('input', function() {
                // تحديث الكمية في window.addedItems
                const itemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);
                if (itemIndex !== -1) {
                    window.addedItems[itemIndex].quantity = parseFloat(this.value) || 1;
                }

                // تحديث خلية الإجمالي
                const quantity = parseFloat(this.value) || 1;
                const price = parseFloat(tr.dataset.itemPrice) || 0;
                const total = quantity * price;

                const totalCell = tr.querySelector('.item-total');
                if (totalCell) {
                    totalCell.textContent = total.toFixed(2);
                }

                updateInvoiceTotal();
                log(`تحديث كمية ${item.name}: ${quantity}, إجمالي جديد: ${total.toFixed(2)}`);
            });

            selectedItemsTable.querySelector('tbody').appendChild(tr);

            // تمييز الصف في الجدول الأصلي
            const originalRow = document.querySelector(`#accountsTable tr[data-item-id="${item.id}"]`);
            if (originalRow) {
                originalRow.classList.add('highlight');
                const addCell = originalRow.querySelector('.add-cell');
                if (addCell) {
                    addCell.innerHTML = `
                        <input type="number" class="form-control quantity-input" min="0.1" step="0.1" value="${correctQuantity}" style="width: 80px; margin: auto;">
                        <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-left: 10px;"></i>
                    `;
                }
            }

            updateInvoiceTotal();
        }

        // إعداد معالج الباركود
        document.addEventListener('DOMContentLoaded', function() {
            const barcodeInput = document.getElementById('barcodeInput');

            barcodeInput.addEventListener('input', function() {
                const barcode = this.value.trim();

                if (barcode.length >= 13) {
                    log(`تم إدخال باركود: ${barcode}`);

                    // معالجة الباركود المخصص
                    const customBarcodeData = processCustomBarcode(barcode);

                    if (customBarcodeData) {
                        const itemId = barcodeItemsMap[customBarcodeData.itemBarcode];
                        if (itemId) {
                            const item = findItemById(itemId);
                            if (item) {
                                item.quantity = customBarcodeData.quantity;
                                item.isCustomBarcode = true;
                                item.customBarcodeData = customBarcodeData;

                                // استخدام الدالة المحدثة
                                if (typeof addItemToSelectedList === 'function') {
                                    addItemToSelectedList(item);
                                } else {
                                    log('خطأ: دالة addItemToSelectedList غير موجودة');
                                }

                                this.value = '';
                            }
                        } else {
                            log(`باركود صنف غير معروف: ${customBarcodeData.itemBarcode}`);
                        }
                    } else {
                        // باركود عادي
                        const itemId = barcodeItemsMap[barcode];
                        if (itemId) {
                            const item = findItemById(itemId);
                            if (item && typeof addItemToSelectedList === 'function') {
                                addItemToSelectedList(item);
                                this.value = '';
                            }
                        } else {
                            log(`باركود غير معروف: ${barcode}`);
                        }
                    }
                }
            });

            log('تم تحميل صفحة الاختبار بنجاح');
        });
    </script>
</body>

</html>