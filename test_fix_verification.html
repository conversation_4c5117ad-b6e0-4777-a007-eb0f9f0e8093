<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح مشكلة الأصناف المختارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .scenario {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .scenario-normal {
            background-color: #e7f3ff;
            border-color: #007bff;
        }
        
        .scenario-responsive {
            background-color: #e8f5e8;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            🔧 اختبار إصلاح مشكلة الأصناف المختارة
        </h1>
        
        <!-- السيناريو الأول: الوضع العادي -->
        <div class="test-section">
            <div class="scenario scenario-normal">
                <h3>📱 السيناريو الأول: الوضع العادي (بدون تقسيم)</h3>
                <p><strong>الحالة:</strong> الشاشة عادية، لا يوجد وضع مقسم نشط</p>
                <p><strong>المتوقع:</strong> عند قراءة الباركود، يجب إضافة الصنف لـ window.addedItems والجدول الأصلي</p>
                
                <button onclick="testNormalMode()" class="btn btn-primary">
                    اختبار الوضع العادي
                </button>
                <div id="normalResult" class="mt-3"></div>
            </div>
        </div>
        
        <!-- السيناريو الثاني: الوضع المقسم -->
        <div class="test-section">
            <div class="scenario scenario-responsive">
                <h3>📺 السيناريو الثاني: الوضع المقسم (متجاوب)</h3>
                <p><strong>الحالة:</strong> الوضع المقسم نشط، يوجد جدول الأصناف المختارة</p>
                <p><strong>المتوقع:</strong> عند قراءة الباركود، يجب إضافة الصنف لجدول الأصناف المختارة</p>
                
                <button onclick="createResponsiveMode()" class="btn btn-success">
                    تفعيل الوضع المقسم
                </button>
                <button onclick="testResponsiveMode()" class="btn btn-info">
                    اختبار الوضع المقسم
                </button>
                <div id="responsiveResult" class="mt-3"></div>
            </div>
        </div>
        
        <!-- حالة البيانات -->
        <div class="test-section">
            <h3>📊 حالة البيانات</h3>
            <button onclick="checkDataStatus()" class="btn btn-secondary">
                فحص حالة البيانات
            </button>
            <div id="dataStatus" class="mt-3"></div>
        </div>
        
        <!-- سجل الاختبارات -->
        <div class="test-section">
            <h3>📝 سجل الاختبارات</h3>
            <div id="testLog" class="test-log">جاهز للاختبار...</div>
            <button onclick="clearLog()" class="btn btn-secondary btn-sm mt-2">مسح السجل</button>
        </div>
    </div>

    <script>
        // محاكاة البيانات المطلوبة
        window.addedItems = [];
        window.barcodeItemsMap = {
            '000001': '1'
        };
        
        // محاكاة categories
        window.categories = [
            {
                id: '1',
                name: 'منتجات الألبان',
                items: [
                    {
                        id: '1',
                        name: 'جبنة تركي',
                        price: 280.00,
                        barcode: '000001'
                    }
                ]
            }
        ];
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };
            
            logElement.textContent += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // محاكاة دالة findItemById
        function findItemById(itemId) {
            for (let i = 0; i < window.categories.length; i++) {
                const category = window.categories[i];
                for (let j = 0; j < category.items.length; j++) {
                    const item = category.items[j];
                    if (item.id === itemId) {
                        return item;
                    }
                }
            }
            return null;
        }
        
        // محاكاة دالة addItemToOriginalTable المحسنة
        function addItemToOriginalTable(item) {
            log(`محاولة إضافة الصنف للجدول الأصلي: ${item.name}`, 'info');
            
            // محاكاة عدم وجود الصف في الجدول الأصلي (الحالة الشائعة)
            const originalRow = null; // محاكاة عدم العثور على الصف
            
            if (!originalRow) {
                log('الصف غير موجود في الجدول الأصلي، إضافة الصنف لـ addedItems', 'warning');
                
                // التحقق من وجود الصنف في addedItems
                const existingItemIndex = window.addedItems.findIndex(addedItem => addedItem.id === item.id);
                
                if (existingItemIndex === -1) {
                    // إضافة الصنف لـ addedItems إذا لم يكن موجوداً
                    let correctQuantity;
                    if (item.isCustomBarcode && item.customBarcodeData && item.customBarcodeData.quantity) {
                        correctQuantity = item.customBarcodeData.quantity;
                    } else {
                        correctQuantity = item.quantity || 1;
                    }
                    
                    window.addedItems.push({
                        id: item.id,
                        name: item.name,
                        quantity: correctQuantity,
                        price: item.price || '0',
                        isCustomBarcode: item.isCustomBarcode || false,
                        customBarcodeData: item.customBarcodeData || null
                    });
                    
                    log(`تم إضافة الصنف لـ addedItems: ${item.name} - الكمية: ${correctQuantity}`, 'success');
                } else {
                    // تحديث الصنف الموجود
                    let correctQuantity;
                    if (item.isCustomBarcode && item.customBarcodeData && item.customBarcodeData.quantity) {
                        correctQuantity = item.customBarcodeData.quantity;
                    } else {
                        correctQuantity = item.quantity || 1;
                    }
                    
                    window.addedItems[existingItemIndex].quantity = correctQuantity;
                    window.addedItems[existingItemIndex].isCustomBarcode = item.isCustomBarcode || false;
                    window.addedItems[existingItemIndex].customBarcodeData = item.customBarcodeData || null;
                    
                    log(`تم تحديث الصنف في addedItems: ${item.name} - الكمية: ${correctQuantity}`, 'success');
                }
                return;
            }
        }
        
        // محاكاة دالة addItemToSelectedList المحسنة
        function addItemToSelectedList(item) {
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            if (!selectedItemsTable) {
                log('جدول الأصناف المختارة غير موجود - الوضع المقسم غير نشط', 'warning');
                log('سيتم إضافة الصنف للجدول الأصلي بدلاً من ذلك', 'info');
                
                // إذا لم يكن الوضع المقسم نشطاً، أضف الصنف للجدول الأصلي مباشرة
                addItemToOriginalTable(item);
                return;
            }
            
            log(`إضافة الصنف لجدول الأصناف المختارة: ${item.name}`, 'success');
            
            // إضافة الصنف للجدول المرئي
            const tr = document.createElement('tr');
            tr.dataset.itemId = item.id;
            tr.innerHTML = `
                <td style="padding: 8px;">${item.name} ${item.isCustomBarcode ? '(مخصص)' : ''}</td>
                <td style="padding: 8px;">${item.price}</td>
                <td style="padding: 8px;">${item.quantity ? item.quantity.toFixed(3) : '1.000'}</td>
                <td style="padding: 8px;">${(item.price * (item.quantity || 1)).toFixed(2)}</td>
                <td style="padding: 8px;">
                    <button onclick="removeItem('${item.id}')" class="btn btn-sm btn-danger">حذف</button>
                </td>
            `;
            
            selectedItemsTable.appendChild(tr);
            
            // إضافة للبيانات أيضاً
            addItemToOriginalTable(item);
        }
        
        // اختبار الوضع العادي
        function testNormalMode() {
            log('=== بدء اختبار الوضع العادي ===', 'info');
            
            // التأكد من عدم وجود جدول الأصناف المختارة
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            if (selectedItemsTable) {
                log('تحذير: جدول الأصناف المختارة موجود! قم بإزالة الوضع المقسم أولاً', 'warning');
            }
            
            // محاكاة قراءة باركود مخصص
            const barcode = '2000001001752';
            const itemBarcode = barcode.substring(1, 7); // 000001
            const weightInGrams = parseInt(barcode.substring(7, 12)); // 01752
            const quantityInKg = weightInGrams / 1000; // 1.752
            
            log(`قراءة باركود مخصص: ${barcode}`, 'info');
            log(`باركود الصنف: ${itemBarcode}, الكمية: ${quantityInKg} كيلو`, 'info');
            
            // البحث عن الصنف
            const itemId = window.barcodeItemsMap[itemBarcode];
            if (!itemId) {
                log(`باركود غير معروف: ${itemBarcode}`, 'error');
                return;
            }
            
            const item = findItemById(itemId);
            if (!item) {
                log(`الصنف غير موجود: ${itemId}`, 'error');
                return;
            }
            
            // إنشاء صنف مخصص
            const customItem = {
                ...item,
                quantity: quantityInKg,
                isCustomBarcode: true,
                customBarcodeData: {
                    itemBarcode: itemBarcode,
                    quantity: quantityInKg,
                    weightInGrams: weightInGrams
                }
            };
            
            // محاولة الإضافة
            const beforeCount = window.addedItems.length;
            addItemToSelectedList(customItem);
            const afterCount = window.addedItems.length;
            
            // عرض النتيجة
            const resultDiv = document.getElementById('normalResult');
            if (afterCount > beforeCount || window.addedItems.some(item => item.id === customItem.id)) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>✅ نجح الاختبار!</strong><br>
                        تم إضافة الصنف بنجاح في الوضع العادي.<br>
                        الصنف: ${customItem.name}<br>
                        الكمية: ${quantityInKg.toFixed(3)} كيلو<br>
                        عدد الأصناف في addedItems: ${window.addedItems.length}
                    </div>
                `;
                log('✅ نجح اختبار الوضع العادي', 'success');
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ فشل الاختبار!</strong><br>
                        لم يتم إضافة الصنف في الوضع العادي.
                    </div>
                `;
                log('❌ فشل اختبار الوضع العادي', 'error');
            }
        }
        
        // إنشاء الوضع المقسم
        function createResponsiveMode() {
            log('إنشاء الوضع المقسم...', 'info');
            
            // إنشاء جدول الأصناف المختارة
            const container = document.createElement('div');
            container.id = 'responsiveTestContainer';
            container.innerHTML = `
                <h4>جدول الأصناف المختارة (محاكاة)</h4>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="selectedItemsTable"></tbody>
                </table>
            `;
            
            // إضافة الحاوية للصفحة
            const responsiveSection = document.querySelector('.scenario-responsive');
            responsiveSection.appendChild(container);
            
            log('تم إنشاء الوضع المقسم بنجاح', 'success');
        }
        
        // اختبار الوضع المقسم
        function testResponsiveMode() {
            log('=== بدء اختبار الوضع المقسم ===', 'info');
            
            // التأكد من وجود جدول الأصناف المختارة
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            if (!selectedItemsTable) {
                log('خطأ: جدول الأصناف المختارة غير موجود! قم بتفعيل الوضع المقسم أولاً', 'error');
                return;
            }
            
            // محاكاة قراءة باركود عادي
            const barcode = '000001';
            const itemId = window.barcodeItemsMap[barcode];
            const item = findItemById(itemId);
            
            if (!item) {
                log(`الصنف غير موجود: ${itemId}`, 'error');
                return;
            }
            
            log(`قراءة باركود عادي: ${barcode}`, 'info');
            log(`الصنف: ${item.name}`, 'info');
            
            // محاولة الإضافة
            const beforeRows = selectedItemsTable.children.length;
            addItemToSelectedList({...item, quantity: 1});
            const afterRows = selectedItemsTable.children.length;
            
            // عرض النتيجة
            const resultDiv = document.getElementById('responsiveResult');
            if (afterRows > beforeRows) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>✅ نجح الاختبار!</strong><br>
                        تم إضافة الصنف بنجاح في الوضع المقسم.<br>
                        الصنف: ${item.name}<br>
                        عدد الصفوف في الجدول: ${afterRows}
                    </div>
                `;
                log('✅ نجح اختبار الوضع المقسم', 'success');
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ فشل الاختبار!</strong><br>
                        لم يتم إضافة الصنف في الوضع المقسم.
                    </div>
                `;
                log('❌ فشل اختبار الوضع المقسم', 'error');
            }
        }
        
        // فحص حالة البيانات
        function checkDataStatus() {
            const statusDiv = document.getElementById('dataStatus');
            
            let html = '<h5>حالة البيانات الحالية:</h5>';
            html += `<p><strong>window.addedItems:</strong> ${window.addedItems.length} عنصر</p>`;
            
            if (window.addedItems.length > 0) {
                html += '<ul>';
                window.addedItems.forEach((item, index) => {
                    html += `<li>${item.name} - الكمية: ${item.quantity} ${item.isCustomBarcode ? '(مخصص)' : ''}</li>`;
                });
                html += '</ul>';
            }
            
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            if (selectedItemsTable) {
                html += `<p><strong>جدول الأصناف المختارة:</strong> ${selectedItemsTable.children.length} صف</p>`;
            } else {
                html += `<p><strong>جدول الأصناف المختارة:</strong> غير موجود (الوضع العادي)</p>`;
            }
            
            statusDiv.innerHTML = html;
            log('تم فحص حالة البيانات', 'info');
        }
        
        // حذف صنف
        function removeItem(itemId) {
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            const row = selectedItemsTable.querySelector(`tr[data-item-id="${itemId}"]`);
            if (row) {
                row.remove();
                log(`تم حذف الصنف: ${itemId}`, 'info');
            }
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('testLog').textContent = 'تم مسح السجل...\n';
        }
        
        // تسجيل بداية الاختبار
        log('تم تحميل أداة اختبار الإصلاح بنجاح', 'success');
        log('💡 ابدأ باختبار الوضع العادي أولاً', 'info');
    </script>
</body>
</html>
