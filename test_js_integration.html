<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل JavaScript</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار تكامل JavaScript</h1>
        <p>هذا الملف لاختبار أن ملف add_invoice.js يعمل بشكل صحيح</p>
        
        <div id="test-results"></div>
        
        <button onclick="runTests()">تشغيل الاختبارات</button>
        <button onclick="clearResults()">مسح النتائج</button>
    </div>

    <script>
        // محاكاة البيانات التي يتم تمريرها من PHP
        window.invoiceCategories = [
            {id: 1, name: "فئة تجريبية"}
        ];
        window.invoiceItemsByCategory = {
            1: [{id: 1, name: "منتج تجريبي", price: "10.00"}]
        };
        window.encryptedStoreId = 'test_store_id';
        window.encryptedAccountId = 'test_account_id';
        window.encryptedAccountsMap = {};
        window.addedItems = [];
        window.canCreatePurchase = true;
        window.canCreateWholesale = true;
        window.userTheme = 'Light';
        window.savedInvoiceType = 'purchase';
        window.savedBranchId = '';
        window.savedAccountBuyerId = '';

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function runTests() {
            clearResults();
            addTestResult('بدء الاختبارات...', 'info');

            // اختبار 1: التحقق من وجود المتغيرات العامة
            try {
                if (typeof window.encryptedStoreId !== 'undefined') {
                    addTestResult('✓ متغير encryptedStoreId موجود', 'success');
                } else {
                    addTestResult('✗ متغير encryptedStoreId غير موجود', 'error');
                }

                if (typeof window.addedItems !== 'undefined') {
                    addTestResult('✓ متغير addedItems موجود', 'success');
                } else {
                    addTestResult('✗ متغير addedItems غير موجود', 'error');
                }

                if (typeof window.canCreatePurchase !== 'undefined') {
                    addTestResult('✓ متغير canCreatePurchase موجود', 'success');
                } else {
                    addTestResult('✗ متغير canCreatePurchase غير موجود', 'error');
                }
            } catch (error) {
                addTestResult('✗ خطأ في اختبار المتغيرات: ' + error.message, 'error');
            }

            // اختبار 2: التحقق من تحميل ملف JavaScript
            try {
                // محاولة الوصول لدالة من الملف
                if (typeof window.playSound === 'function') {
                    addTestResult('✓ دالة playSound متاحة', 'success');
                } else {
                    addTestResult('⚠ دالة playSound غير متاحة (قد تكون لم تحمل بعد)', 'error');
                }
            } catch (error) {
                addTestResult('✗ خطأ في اختبار الدوال: ' + error.message, 'error');
            }

            // اختبار 3: التحقق من Theme
            try {
                if (window.userTheme === 'Light') {
                    addTestResult('✓ Theme تم تعيينه بشكل صحيح', 'success');
                } else {
                    addTestResult('⚠ Theme: ' + window.userTheme, 'info');
                }
            } catch (error) {
                addTestResult('✗ خطأ في اختبار Theme: ' + error.message, 'error');
            }

            addTestResult('انتهت الاختبارات', 'info');
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(runTests, 1000); // انتظار ثانية واحدة للتأكد من تحميل كل شيء
        });
    </script>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="js/add_invoice.js"></script>
</body>
</html>
