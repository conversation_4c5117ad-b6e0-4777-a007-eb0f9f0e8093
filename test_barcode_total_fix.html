<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح حساب إجمالي الباركود المخصص</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .calculation-details {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح حساب إجمالي الباركود المخصص</h1>
        <p>هذا الاختبار يحاكي عملية إضافة صنف بباركود مخصص ويتحقق من صحة حساب الإجمالي</p>
        
        <div>
            <label>باركود مخصص للاختبار:</label>
            <input type="text" id="testBarcode" value="2000001012500" placeholder="أدخل باركود مخصص">
            <button onclick="testBarcodeCalculation()">اختبار الحساب</button>
        </div>
        
        <div id="test-results"></div>
        
        <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <button onclick="clearResults()">مسح النتائج</button>
    </div>

    <script>
        // محاكاة بيانات الأصناف
        const testItems = {
            '000001': {
                id: '1',
                name: 'جبنة تركي',
                price: '280.00',
                barcode: '000001'
            },
            '000002': {
                id: '2', 
                name: 'لحم بقري',
                price: '450.00',
                barcode: '000002'
            }
        };

        // محاكاة window.addedItems
        window.addedItems = [];

        // دالة معالجة الباركود المخصص (نسخة من الكود الأصلي)
        function processCustomBarcode(barcode) {
            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        // محاكاة دالة addItemToSelectedList المحدثة
        function simulateAddItemToSelectedList(item) {
            const itemPrice = item.price;

            // تحديد الكمية الصحيحة للإضافة (الكود المحدث)
            let correctQuantity;
            if (item.isCustomBarcode && item.customBarcodeData && item.customBarcodeData.quantity) {
                correctQuantity = item.customBarcodeData.quantity;
                console.log('إضافة صنف بباركود مخصص - الكمية من الباركود:', correctQuantity);
            } else {
                correctQuantity = item.quantity || 1;
                console.log('إضافة صنف عادي - الكمية:', correctQuantity);
            }

            // إضافة العنصر إلى window.addedItems
            window.addedItems.push({
                id: item.id,
                name: item.name,
                quantity: correctQuantity,
                price: itemPrice,
                isCustomBarcode: item.isCustomBarcode || false,
                customBarcodeData: item.customBarcodeData || null
            });

            // حساب الإجمالي - استخدام الكمية الصحيحة
            const itemTotal = correctQuantity * parseFloat(itemPrice || 0);
            
            return {
                quantity: correctQuantity,
                price: parseFloat(itemPrice),
                total: itemTotal
            };
        }

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            window.addedItems = [];
        }

        function testBarcodeCalculation() {
            const barcode = document.getElementById('testBarcode').value.trim();
            
            if (!barcode) {
                addTestResult('يرجى إدخال باركود للاختبار', 'warning');
                return;
            }

            addTestResult(`<strong>اختبار الباركود:</strong> ${barcode}`, 'info');

            // معالجة الباركود المخصص
            const customBarcodeData = processCustomBarcode(barcode);
            
            if (!customBarcodeData) {
                addTestResult('الباركود المدخل ليس باركود مخصص صحيح', 'error');
                return;
            }

            // البحث عن الصنف
            const item = testItems[customBarcodeData.itemBarcode];
            if (!item) {
                addTestResult(`صنف غير موجود للباركود: ${customBarcodeData.itemBarcode}`, 'error');
                return;
            }

            // إعداد بيانات الصنف
            const itemToAdd = {
                ...item,
                quantity: customBarcodeData.quantity,
                isCustomBarcode: true,
                customBarcodeData: customBarcodeData
            };

            // محاكاة إضافة الصنف
            const result = simulateAddItemToSelectedList(itemToAdd);

            // عرض النتائج
            addTestResult(`
                <div class="calculation-details">
                    <strong>تفاصيل الحساب:</strong><br>
                    الصنف: ${item.name}<br>
                    الباركود الأصلي: ${barcode}<br>
                    باركود الصنف: ${customBarcodeData.itemBarcode}<br>
                    الوزن بالجرام: ${customBarcodeData.weightInGrams}<br>
                    الكمية بالكيلو: ${customBarcodeData.quantity.toFixed(3)}<br>
                    السعر للكيلو: ${item.price}<br>
                    <strong>الإجمالي المحسوب: ${result.total.toFixed(2)}</strong><br>
                    <br>
                    المعادلة: ${result.quantity.toFixed(3)} × ${result.price} = ${result.total.toFixed(2)}
                </div>
            `, 'success');

            // التحقق من صحة الحساب
            const expectedTotal = customBarcodeData.quantity * parseFloat(item.price);
            if (Math.abs(result.total - expectedTotal) < 0.01) {
                addTestResult('✅ الحساب صحيح!', 'success');
            } else {
                addTestResult(`❌ خطأ في الحساب! المتوقع: ${expectedTotal.toFixed(2)}، الفعلي: ${result.total.toFixed(2)}`, 'error');
            }
        }

        function runAllTests() {
            clearResults();
            
            const testCases = [
                '2000001012500', // جبنة تركي - 1.250 كيلو
                '2000001005000', // جبنة تركي - 0.500 كيلو  
                '2000002020000', // لحم بقري - 2.000 كيلو
                '2000002007500'  // لحم بقري - 0.750 كيلو
            ];

            addTestResult('<strong>تشغيل جميع الاختبارات...</strong>', 'info');

            testCases.forEach((testBarcode, index) => {
                addTestResult(`<hr><strong>اختبار ${index + 1}:</strong>`, 'info');
                document.getElementById('testBarcode').value = testBarcode;
                testBarcodeCalculation();
            });

            addTestResult('<hr><strong>انتهت جميع الاختبارات</strong>', 'info');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addTestResult('مرحباً! اضغط على "تشغيل جميع الاختبارات" لاختبار إصلاح حساب الإجمالي', 'info');
            }, 500);
        });
    </script>
</body>
</html>
