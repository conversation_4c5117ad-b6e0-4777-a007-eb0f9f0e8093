/* متغيرات الألوان */
:root {
    --color-bg: linear-gradient(to right, #f5f7fa, #c3cfe2);
    --color-fg: #333;
    --color-primary: #3f51b5;
    --color-secondary: #ffffff;
    --color-hover: rgba(63, 81, 181, 0.1);
    --color-header-text: #ffffff;
    --color-button-text: #ffffff;
    --input-bg: #ffffff;
    --input-border: #e9ecef;
    --input-text: #333;
    --table-header-bg: linear-gradient(135deg, #495057, #6c757d);
    --table-row-hover: #f8f9fa;
    --progress-bg: #e9ecef;
    --badge-info-bg: #17a2b8;
    --badge-success-bg: #28a745;
    --badge-danger-bg: #dc3545;
    --badge-secondary-bg: #6c757d;
}

[data-theme="dark"] {
    --color-bg: #0d1117;
    --color-fg: #c9d1d9;
    --color-primary: #58a6ff;
    --color-secondary: #161b22;
    --color-hover: rgba(88, 166, 255, 0.08);
    --color-header-text: #ffffff;
    --color-button-text: #ffffff;
    --input-bg: #21262d;
    --input-border: #30363d;
    --input-text: #c9d1d9;
    --table-header-bg: linear-gradient(135deg, #21262d, #30363d);
    --table-row-hover: rgba(88, 166, 255, 0.05);
    --progress-bg: #30363d;
    --badge-info-bg: #1f6feb;
    --badge-success-bg: #238636;
    --badge-danger-bg: #da3633;
    --badge-secondary-bg: #656d76;
}

.barcode-col {
    display: none !important;
}

.barcode-highlighted {
    background-color: #007bff !important;
    color: white !important;
    animation: pulse 1s ease-in-out;
}

.barcode-highlighted td {
    background-color: transparent !important;
    color: white !important;
}

.barcode-highlighted input {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #333 !important;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* تحسينات واجهة المستخدم */
.inventory-header {
    color: var(--color-header-text);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.inventory-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: var(--color-secondary);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid var(--color-primary);
    transition: transform 0.3s ease;
    color: var(--color-fg);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: var(--color-primary);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--color-fg);
    opacity: 0.7;
    font-size: 0.9em;
}

.progress-container {
    background: var(--color-secondary);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: var(--color-fg);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: var(--progress-bg);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.search-container-enhanced {
    background: var(--color-secondary);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: var(--color-fg);
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-input-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 2;
}

#searchField {
    padding-left: 45px !important;
    border: 2px solid var(--input-border);
    border-radius: 25px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: var(--input-bg);
    color: var(--input-text);
}

#searchField:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.25);
    outline: none;
}

#searchField::placeholder {
    color: var(--color-fg);
    opacity: 0.6;
}

.quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.quick-btn {
    padding: 8px 16px;
    border: 1px solid var(--input-border);
    border-radius: 20px;
    background: var(--input-bg);
    color: var(--color-fg);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.quick-btn:hover {
    background: var(--color-primary);
    color: var(--color-button-text);
    transform: translateY(-2px);
    border-color: var(--color-primary);
}

.quick-btn.active {
    background: var(--color-primary);
    color: var(--color-button-text);
    border-color: var(--color-primary);
}

.table-container-enhanced {
    background: var(--color-secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table-header-enhanced {
    background: var(--table-header-bg);
    color: var(--color-header-text);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-row {
    transition: all 0.3s ease;
    cursor: pointer;
}

.item-row:hover {
    background-color: var(--table-row-hover);
    transform: scale(1.01);
}

.quantity-input-enhanced {
    border: 2px solid var(--input-border);
    border-radius: 8px;
    padding: 8px 12px;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s ease;
    background-color: var(--input-bg);
    color: var(--input-text);
}

.quantity-input-enhanced:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.25);
    outline: none;
}

.quantity-input-enhanced.filled {
    background-color: #d4edda;
    border-color: var(--badge-success-bg);
    color: #155724;
    font-weight: bold;
}

/* تحسين الوضع المظلم لحقول الكمية */
[data-theme="dark"] .quantity-input-enhanced.filled {
    background-color: rgba(35, 134, 54, 0.3);
    border-color: var(--badge-success-bg);
    color: var(--badge-success-bg);
    font-weight: bold;
}

.floating-summary {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--color-secondary);
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    z-index: 3; /* أعلى من جميع العناصر الأخرى */
    min-width: 200px;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    color: var(--color-fg);
    border: 1px solid var(--color-primary);
}

.floating-summary.show {
    transform: translateY(0);
    opacity: 1;
}

.back-to-top {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    background: var(--color-primary);
    color: var(--color-button-text);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
}

.back-to-top:hover {
    background: #303f9f;
    transform: scale(1.1);
}

/* تحسينات للهاتف */
@media (max-width: 768px) {
    .inventory-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-number {
        font-size: 1.5em;
    }
    
    .quick-actions {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .quick-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }
    
    .floating-summary {
        bottom: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* تحسين مظهر الجدول */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
}

.table th {
    background: var(--table-header-bg);
    color: var(--color-header-text);
    font-weight: 600;
    text-align: center;
    padding: 15px 10px;
    border: none;
}

.table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-color: var(--input-border);
    color: var(--color-fg);
    background-color: var(--color-secondary);
}

/* تحسين الوضع المظلم للجدول */
[data-theme="dark"] .table {
    background-color: var(--color-secondary);
}

[data-theme="dark"] .table td {
    border-color: var(--input-border);
}

[data-theme="dark"] .table-bordered {
    border-color: var(--input-border);
}

/* مؤشر الحالة */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-completed { background-color: #28a745; }
.status-pending { background-color: #ffc107; }
.status-empty { background-color: #dc3545; }

/* تحسينات إضافية */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
}

.badge-info {
    background-color: var(--badge-info-bg);
    color: white;
}

.badge-success {
    background-color: var(--badge-success-bg);
    color: white;
}

.badge-danger {
    background-color: var(--badge-danger-bg);
    color: white;
}

.badge-secondary {
    background-color: var(--badge-secondary-bg);
    color: white;
}

/* تحسين الأزرار */
.add-btn {
    background: linear-gradient(135deg, var(--color-primary), #303f9f);
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    color: var(--color-button-text);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(63, 81, 181, 0.4);
}

/* تحسين الاستجابة */
@media (max-width: 576px) {
    .inventory-header {
        padding: 15px;
    }
    
    .inventory-header h2 {
        font-size: 1.5rem;
    }
    
    .table th, .table td {
        padding: 8px 5px;
        font-size: 0.85em;
    }
    
    .quantity-input-enhanced {
        padding: 6px 8px;
        font-size: 0.9em;
    }
}

/* تأثيرات حركية للصفوف */
.item-row.completed {
    background: linear-gradient(90deg, #d4edda, transparent);
    border-left: 4px solid #28a745;
}

.item-row.error {
    background: linear-gradient(90deg, #f8d7da, transparent);
    border-left: 4px solid #dc3545;
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-hover);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #303f9f;
}
/* حاوية الآلة الحاسبة */
#calculator {
    position: fixed;
    top: 50px;
    left: 50px; 
    width: 280px;
    background: var(--color-secondary);
    border: 1px solid var(--input-border);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    overflow: hidden;
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    resize: both;     
    overflow: auto;    
  }
  
  /* رأس الآلة */
  #calcHeader {
    cursor: move;
    background: var(--table-header-bg);
    color: var(--color-header-text);
    padding: 10px;
    font-size: 18px;
    text-align: center;
  }
  
  /* شاشة الإدخال */
  #calcDisplay {
    width: 100%;
    padding: 10px;
    border: none;
    text-align: right;
    font-size: 20px;
    outline: none;
    background-color: var(--input-bg);
    color: var(--input-text);
  }
  
  /* شاشة النتائج */
  #calcResult {
    width: 100%;
    padding: 10px;
    text-align: right;
    font-weight: bold;
    background-color: var(--progress-bg);
    color: var(--color-fg);
    font-size: 18px;
  }
  
  /* أزرار الآلة */
  #calcButtons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    padding: 10px;
  }
  
  #calcButtons button {
    padding: 15px;
    font-size: 16px;
    border: 1px solid var(--input-border);
    border-radius: 5px;
    background: var(--input-bg);
    color: var(--color-fg);
    transition: background 0.3s, transform 0.1s;
    cursor: pointer;
  }
  
  #calcButtons button:hover {
    background: var(--color-primary);
    color: var(--color-button-text);
    border-color: var(--color-primary);
  }
  
  #calcButtons button:active {
    transform: scale(0.95);
  }

/* تحسينات إضافية للوضع المظلم */

/* تحسين الباركود المميز */
[data-theme="dark"] .barcode-highlighted {
    background-color: var(--color-primary) !important;
    color: white !important;
}

[data-theme="dark"] .barcode-highlighted td {
    background-color: transparent !important;
    color: white !important;
}

[data-theme="dark"] .barcode-highlighted input {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #333 !important;
}

/* تحسين الصفوف المكتملة والخطأ */
[data-theme="dark"] .item-row.completed {
    background: linear-gradient(90deg, rgba(35, 134, 54, 0.2), transparent);
    border-left: 4px solid var(--badge-success-bg);
}

[data-theme="dark"] .item-row.error {
    background: linear-gradient(90deg, rgba(218, 54, 51, 0.2), transparent);
    border-left: 4px solid var(--badge-danger-bg);
}

/* تحسين شريط التمرير للوضع المظلم */
[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--color-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--color-primary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #4dabf7;
}

/* تحسين أيقونة البحث */
[data-theme="dark"] .search-input-wrapper i {
    color: var(--color-fg);
    opacity: 0.7;
}

/* تحسين مؤشرات الحالة */
[data-theme="dark"] .status-completed { 
    background-color: var(--badge-success-bg); 
}

[data-theme="dark"] .status-pending { 
    background-color: #ffc107; 
}

[data-theme="dark"] .status-empty { 
    background-color: var(--badge-danger-bg); 
}

/* تحسين الأرقام في الخانات */
.quantity-input-enhanced::-webkit-outer-spin-button,
.quantity-input-enhanced::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.quantity-input-enhanced[type=number] {
    -moz-appearance: textfield;
}

/* تحسين placeholder للوضع المظلم */
[data-theme="dark"] .quantity-input-enhanced::placeholder {
    color: var(--color-fg);
    opacity: 0.5;
}

/* تحسين التركيز على الحقول */
[data-theme="dark"] .quantity-input-enhanced:focus {
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.3);
}

/* تحسين الخلفية العامة */
[data-theme="dark"] body {
    background: var(--color-bg);
    color: var(--color-fg);
}

/* تحسين النصوص */
[data-theme="dark"] .inventory-header h2,
[data-theme="dark"] .inventory-header p {
    color: var(--color-fg);
}

/* تحسين الأزرار في الوضع المظلم */
[data-theme="dark"] .add-btn {
    background: linear-gradient(135deg, var(--color-primary), #4dabf7);
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .add-btn:hover {
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
}
.back-to-top {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

.back-to-top i {
    font-size: 18px;
}

/* تحسين الملخص العائم */
.floating-summary {
    position: fixed;
    bottom: 80px;
    left: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    z-index: 1001;
    min-width: 180px;
    transform: translateX(-100px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
}

.floating-summary.show {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
}

.summary-content {
    text-align: center;
    color: #333;
}

/* تحسين للشاشات الص��يرة */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 15px;
        left: 15px;
        width: 45px;
        height: 45px;
    }
    
    .back-to-top i {
        font-size: 16px;
    }
    
    .floating-summary {
        bottom: 70px;
        left: 15px;
        right: 15px;
        min-width: auto;
        padding: 12px 15px;
    }
}
/* أنماط التنبيهات */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

[data-theme="dark"] .alert-info {
    color: #9fdbef;
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.2);
}

[data-theme="dark"] .alert-success {
    color: #9fdf9f;
    background-color: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.2);
}

[data-theme="dark"] .alert-warning {
    color: #ffd93d;
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
}

/* أنماط مؤشر حالة الاتصال */
.connection-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-block;
    margin-right: 10px;
}

.connection-status.online {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.connection-status.offline {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    animation: pulse 2s infinite;
}

[data-theme="dark"] .connection-status.online {
    background-color: rgba(40, 167, 69, 0.2);
    color: #9fdf9f;
    border-color: rgba(40, 167, 69, 0.3);
}

[data-theme="dark"] .connection-status.offline {
    background-color: rgba(220, 53, 69, 0.2);
    color: #f5c6cb;
    border-color: rgba(220, 53, 69, 0.3);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* أنماط التنبيه المحلي */
.offline-alert {
    border-right: 4px solid #ffc107;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسين مؤشر الحفظ */
#saveIndicator {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 8px;
}

/* أنماط زر المزامنة */
#syncButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#syncButton.syncing {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

[data-theme="dark"] #syncButton.syncing {
    background-color: rgba(255, 193, 7, 0.8);
    border-color: rgba(255, 193, 7, 0.8);
    color: #000;
}

/* عداد العمليات المعلقة */
.pending-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* زر المزامنة عند وجود عمليات معلقة */
#syncButton.has-pending {
    position: relative;
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

[data-theme="dark"] #syncButton.has-pending {
    background-color: rgba(255, 193, 7, 0.9);
    border-color: rgba(255, 193, 7, 0.9);
    color: #000;
}

#syncButton {
    position: relative;
}

/* تحسين حقول الإدخال المملوءة */
.quantity-input-enhanced.filled {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.1);
}

[data-theme="dark"] .quantity-input-enhanced.filled {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

/* تحسين الصفوف المميزة بالباركود */
.barcode-highlighted {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
    border: 2px solid #2196f3 !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3) !important;
    transform: scale(1.02) !important;
    transition: all 0.3s ease !important;
}

[data-theme="dark"] .barcode-highlighted {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%) !important;
    border-color: #2196f3 !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4) !important;
}
/* شريط البحث الثابت */
.search-container-enhanced {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.search-container-enhanced.sticky {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-radius: 0;
    margin-bottom: 0;
    padding: 10px 15px;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: 2px solid #007bff;
}

/* تحسين تصميم الأزرار */
.action-buttons-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 15px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.action-buttons-container:hover {
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.action-buttons-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: stretch;
}

.action-btn-wrapper {
    flex: 1;
    min-width: 200px;
    max-width: 250px;
}

.action-btn {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 600;
    font-size: 14px;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.action-btn i {
    font-size: 18px;
}

/* تحسين ألوان الأزرار */
.btn-success.action-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
}

.btn-primary.action-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
}

.btn-danger.action-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
}

.btn-info.action-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.btn-warning.action-btn {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-color: #ffc107;
    color: #212529;
}

.btn-secondary.action-btn {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border-color: #6c757d;
}

.btn-dark.action-btn {
    background: linear-gradient(135deg, #343a40, #23272b);
    border-color: #343a40;
}

.btn-outline-primary.action-btn {
    background: transparent;
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary.action-btn:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

/* تحسين الشارة المعلقة */
.pending-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn {
    position: relative;
}

/* تحسين الأزرار السريعة */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.quick-btn {
    flex: 1;
    min-width: 120px;
    max-width: 200px;
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,123,255,0.2);
}

.quick-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 4px 10px rgba(0,123,255,0.3);
}

.quick-btn.has-pending {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

.pending-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    margin-right: 5px;
    font-weight: bold;
}

/* تحسين البحث */
.search-input-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-input-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.input-field {
    width: 100%;
    padding: 12px 50px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.input-field:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

/* تحسين الجدول */
.table-container-enhanced {
    margin-top: 20px;
}

/* إزالة التمرير الثابت وجعل الجدول بطول الصفحة الكامل */
.table-responsive {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
}

.table-container-enhanced .table-responsive {
    max-height: none !important;
    height: auto !important;
    overflow-y: visible !important;
    overflow-x: auto; /* الحفاظ على التمرير الأفقي فقط للشاشات الصغيرة */
}

/* تحسين رأس الجدول الثابت */
.table thead th {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #495057, #6c757d);
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.15);
    border: none;
}


/* تحسين الأزرار الرئيسية */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
}

/* تحسين بطاقات الإحصائيات */
.inventory-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
    }
    
    .quick-btn {
        max-width: none;
        margin-bottom: 5px;
    }
    
    .search-container-enhanced {
        padding: 15px;
    }
    
    .action-buttons-container {
        padding: 20px 15px;
        margin: 20px 0;
    }
    
    .action-buttons-row {
        flex-direction: column;
        gap: 12px;
    }
    
    .action-btn-wrapper {
        min-width: auto;
        max-width: none;
    }
    
    .action-btn {
        height: 55px;
        font-size: 13px;
    }
    
    .action-btn i {
        font-size: 16px;
    }
    
    .inventory-stats {
        flex-direction: column;
    }
    
    .stat-card {
        min-width: auto;
    }
    
}

@media (max-width: 480px) {
    .action-buttons-container {
        padding: 15px 10px;
    }
    
    .action-btn {
        height: 50px;
        font-size: 12px;
        gap: 8px;
    }
    
    .action-btn i {
        font-size: 14px;
    }
}
/* تحسينات نافذة تقدم إكمال الجرد */
.swal2-popup.text-right {
    text-align: right !important;
    direction: rtl !important;
}

.swal2-popup .step-item {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.swal2-popup .step-item:hover {
    background-color: #f1f3f4 !important;
    border-color: #dee2e6;
}

.swal2-popup .step-icon {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.swal2-popup #overallProgressBar {
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.swal2-popup #currentStepProgress {
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* تحسين الألوان للخطوات */
.step-completed {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
}

.step-current {
    background-color: #e3f2fd !important;
    border-color: #bbdefb !important;
    animation: pulse 2s infinite;
}

.step-pending {
    background-color: #f8f9fa !important;
    border-color: #e9ecef !important;
}

.step-failed {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

/* تحسين شريط التقدم */
.progress-bar-animated {
    background: linear-gradient(45deg, 
        rgba(255,255,255,.15) 25%, 
        transparent 25%, 
        transparent 50%, 
        rgba(255,255,255,.15) 50%, 
        rgba(255,255,255,.15) 75%, 
        transparent 75%, 
        transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* تحسين الأيقونات */
.step-icon i {
    font-size: 10px;
}

.step-icon.completed {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.step-icon.current {
    background: linear-gradient(135deg, #007bff, #6610f2) !important;
}

.step-icon.failed {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

/* تحسين النصوص */
.step-name {
    font-weight: 500;
    font-size: 0.9em;
}

.step-status {
    font-size: 0.75em;
    font-weight: 400;
}

/* تحسين التنبيهات */
.inventory-alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.inventory-alert.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

.inventory-alert.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.inventory-alert.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

/* تحسين الأزرار */
.swal2-confirm, .swal2-cancel {
    border-radius: 6px !important;
    font-weight: 500 !important;
    padding: 10px 20px !important;
    transition: all 0.3s ease !important;
}

.swal2-confirm:hover, .swal2-cancel:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تحسين النافذة الرئيسية */
.swal2-popup {
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2) !important;
}

.swal2-title {
    font-size: 1.5em !important;
    font-weight: 600 !important;
}

/* تحسين التمرير */
.steps-container {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

.steps-container::-webkit-scrollbar {
    width: 6px;
}

.steps-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.steps-container::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.steps-container::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .swal2-popup {
        width: 95% !important;
        margin: 0 auto !important;
    }
    
    .step-item {
        padding: 6px !important;
        font-size: 0.9em;
    }
    
    .step-icon {
        width: 18px !important;
        height: 18px !important;
        font-size: 10px !important;
    }
    
    .swal2-title {
        font-size: 1.3em !important;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين مؤشر التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسين الحالات المختلفة للخطوات */
.step-item.completed .step-name {
    color: #155724 !important;
    font-weight: 600;
}

.step-item.current .step-name {
    color: #1976d2 !important;
    font-weight: 600;
}

.step-item.failed .step-name {
    color: #721c24 !important;
    font-weight: 600;
}

.step-item.pending .step-name {
    color: #6c757d !important;
}

/* تحسين شريط التقدم الفرعي */
#currentStepProgress {
    background: linear-gradient(90deg, #28a745, #20c997) !important;
    border-radius: 4px;
}

/* تحسين المعلومات الإضافية */
.info-box {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 12px;
    margin-top: 15px;
}

.info-box i {
    margin-left: 8px;
    color: #856404;
}

/* تحسين عرض الأخطاء */
.error-details {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
    font-size: 0.9em;
    color: #721c24;
}

/* تحسين الن��اح */
.success-summary {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.success-summary ul {
    margin: 10px 0;
    padding-right: 20px;
}

.success-summary li {
    margin-bottom: 5px;
    color: #155724;
}

.success-summary i {
    color: #155724;
    margin-left: 8px;
}
