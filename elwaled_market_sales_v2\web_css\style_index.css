/*
Theme Name: ShopLite
Theme URI: https://templatejungle.com/
Author: Templates<PERSON><PERSON>le
Author URI: https://templatesjungle.com/
Description: ShopLite is a website template for simple ecommerce websites.
Version: 1.1
*/


/*--------------------------------------------------------------
This is main CSS file that contains custom style rules used in this template
--------------------------------------------------------------*/


/*------------------------------------*\
    Table of contents
\*------------------------------------*/


/*------------------------------------------------

CSS STRUCTURE:
1. VARIABLES

2. GENERAL TYPOGRAPHY
  2.1 General Styles
  2.2 Section Title
  2.3 Buttons

3. CONTENT ELEMENTS
  3.1 Form Control
  3.2 Form Select
  3.3 Table
  3.4 Pagination
  3.5 Slash Divider

4. SITE STRUCTURE
  4.1 Header
  4.2 Billboard
  4.3 Icon Box - Company Services
  4.4 Product
  4.5 Items Listings
  4.6 Testimonial
  4.7 Brands
  4.8 Instagram
  4.9 Footer

5. OTHER PAGES
  5.1 Single Product Page
    - Product Tabs
    
/*--------------------------------------------------------------
/** 1. VARIABLES
--------------------------------------------------------------*/

:root {
    --accent-color: #717171;
    --white-color: #fff;
    --black-color: #272727;
    --gray-color: #F3F3F3;
    --gray-color-200: #E3E3E3;
    --gray-color-300: #E0E0E0;
    --gray-color-500: #D0D0D0;
    --gray-color-800: #3A3A3A;
    --light-gray-color: #D2D2D2;
    --primary-color: #FF6543;
    --bs-body-color: #272727;
    --bs-secondary-color: rgba(194, 194, 194, 1);
    --bs-secondary-rgb: 220, 220, 220;
    --bs-primary-rgb: 255, 101, 67;
    --bs-border-color: #E3E3E3;
    --bs-dropdown-link-active-bg: #F5F5F5;
    --light-color: #F8F8F8;
    --light-blue-color: #EDF1F3;
    --navbar-color-color: #131814;
    --swiper-theme-color: #4A4A4A;
    --swiper-pagination-color: #4A4A4A;
    --bs-box-shadow: 0 0.2rem 0.6rem rgba(0, 0, 0, 0.08);
    --bs-btn-font-size: 1rem;
}


/* on mobile devices below 600px
 */

@media screen and (max-width: 600px) {
     :root {
        --header-height: 100px;
        --header-height-min: 80px;
    }
}


/* Fonts */

:root {
    --body-font: "Outfit", sans-serif;
    --heading-font: "Outfit", sans-serif;
}

@media (min-width: 1850px) {
    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl,
    .container-xxl {
        max-width: 1540px;
    }
}


/*----------------------------------------------*/


/* 2. GENERAL TYPOGRAPHY */


/*----------------------------------------------*/


/* 2.1 General Styles
/*----------------------------------------------*/

*,
*::before,
*::after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    box-sizing: border-box;
}

body {
    font-family: var(--body-font);
    font-size: 21px;
    font-weight: 200;
    margin: 0;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.8;
    letter-spacing: 0;
}

p {
    color: var(--black-color);
    letter-spacing: 0.01rem;
    line-height: normal;
}

a {
    color: var(--black-color);
    text-decoration: none;
    transition: 0.3s color ease-out;
}

a.light {
    color: var(--light-color);
}

a:hover {
    text-decoration: none;
    color: var(--primary-color);
}

hr {
    margin: 1.25rem 0;
    color: var(--gray-color-200);
    opacity: 1;
}


/*------------ Background Color -----------*/

.bg-gray {
    background: var(--gray-color);
}

.bg-dark {
    background: var(--black-color);
}

.bg-light {
    background: var(--light-color);
}

.bg-light-gray {
    background: #F5F5F5;
}


/* - Section Padding
--------------------------------------------------------------*/

.padding-xsmall {
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.padding-small {
    padding-top: 2em;
    padding-bottom: 2em;
}

.padding-medium {
    padding-top: 4em;
    padding-bottom: 4em;
}

.padding-large {
    padding-top: 7em;
    padding-bottom: 7em;
}

.padding-xlarge {
    padding-top: 9.5em;
    padding-bottom: 9.5em;
}


/* - Section margin
--------------------------------------------------------------*/

.margin-small {
    margin-top: 2em;
    margin-bottom: 2em;
}

.margin-medium {
    margin-top: 4em;
    margin-bottom: 4em;
}

.margin-large {
    margin-top: 7em;
    margin-bottom: 7em;
}

.margin-xlarge {
    margin-top: 9.5em;
    margin-bottom: 9.5em;
}


/* 2.2 Section Title
/*----------------------------------------------*/

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--heading-font);
    text-transform: uppercase;
    letter-spacing: 0.06em;
    font-weight: 200;
    line-height: 115%;
}

h4,
h5,
h6 {
    color: var(--light-black-color);
}

h1 {
    font-size: 3.75rem;
}

h2 {
    font-size: 3.188rem;
}

h3 {
    font-size: 1.875rem;
}

h4 {
    font-size: 1.375rem;
}

h5 {
    font-size: 1.313rem;
    font-weight: 300;
    text-transform: capitalize;
    letter-spacing: 0.01rem;
}

h6 {
    font-size: 1.25rem;
}

.section-title h3 {
    width: max-content;
}

.section-title h3::after {
    content: "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////";
    font-size: 19px;
    color: var(--gray-color-500);
    letter-spacing: 0.06rem;
    margin-left: 16px;
}

@media only screen and (max-width: 450px) {
    .section-title h3 {
        width: auto;
    }
    .section-title h3::after {
        display: none;
    }
    #items-listing .section-title h3::after,
    aside .sidebar .section-title h3::after {
        display: flex;
        flex-direction: column;
    }
}

#items-listing .section-title h3::after {
    margin-top: 20px;
}

#items-listing .section-title h3::after,
aside .sidebar .section-title h3::after {
    margin-left: 0;
}

@media only screen and (max-width: 999px) {
    h1 {
        font-size: 3rem;
    }
    h2 {
        font-size: 2.95rem;
    }
    h3 {
        font-size: 1.5rem;
    }
    h4 {
        font-size: 1.24rem;
    }
    h5 {
        font-size: 1.20rem;
    }
}

@media only screen and (max-width: 500px) {
    h1 {
        font-size: 2.6rem;
    }
    h2 {
        font-size: 2rem;
    }
}


/*--------------------------------------------------------------
/** 2.3 Buttons
--------------------------------------------------------------*/

.btn {
    font-weight: 50;
    text-transform: uppercase;
    letter-spacing: 0.09em;
    color: var(--white-color);
    background-color: var(--primary-color);
    border: none;
    padding: 1.125rem 2.625rem;
    font-size: 1rem;
    border-radius: 200px;
    cursor: pointer;
}

.btn:hover,
.btn:focus-visible,
.btn-check:checked+.btn,
.btn.active,
.btn.show,
.btn:first-child:active,
 :not(.btn-check)+.btn:active {
    color: var(--white-color);
    background-color: var(--black-color);
}

.btn-dark {
    background-color: var(--black-color);
}

.btn-dark:hover {
    background-color: var(--primary-color);
}


/* Add new class for delete button */

.delete-btn-small {
    padding: 0.1rem 0.3rem;
    font-size: 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.delete-btn-small:hover {
    background-color: #c82333;
}

.delete-btn-small:active {
    background-color: #bd2130;
}


/*--------------------------------------------------------------
/** 3. CONTENT ELEMENTS
--------------------------------------------------------------*/


/*--------------------------------------------------------------
/** 3.1 Form Control
--------------------------------------------------------------*/

.form-control {
    padding: 1rem;
    font-size: 21px;
    font-weight: 200;
    letter-spacing: 0.01rem;
    line-height: normal;
}

.form-control:focus {
    box-shadow: none;
    border-color: #131814;
}

input.form-control::placeholder,
textarea.form-control::placeholder {
    color: var(--black-color);
}


/*--------------------------------------------------------------
/** 3.2 Form Select
--------------------------------------------------------------*/

.filter-blog .form-select,
.filter-shop .form-select {
    padding: 0;
    font-size: 21px;
    font-weight: 200;
    border: none;
    background-color: transparent;
    line-height: normal;
    width: 180px;
}

.form-select:focus {
    box-shadow: none;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(243, 141, 68, 0.25);
}


/*--------------------------------------------------------------
/** 3.3 Table
--------------------------------------------------------------*/

th {
    font-weight: 200;
    letter-spacing: 0.06rem;
}


/*--------------------------------------------------------------
/** 3.4 Pagination
--------------------------------------------------------------*/

.page-link {
    padding: 0;
    font-size: 21px;
    color: var(--black-color);
    background: none;
    border: none;
}

.page-link:focus {
    color: var(--primary-color);
    background: none;
    box-shadow: none;
}

.page-link:hover {
    color: var(--primary-color);
    background: none;
    border: none;
}

.active>.page-link,
.page-link.active {
    color: var(--primary-color);
    background: none;
    border: none;
}

.disabled>.page-link,
.page-link.disabled {
    color: var(--gray-color-500);
    background: none;
    border: none;
}


/*--------------------------------------------------------------
/** 3.5 Slash Divider
--------------------------------------------------------------*/

.slash-divider {
    overflow: hidden;
}

.slash-divider::after {
    content: "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////";
    font-size: 19px;
    color: var(--gray-color-500);
    letter-spacing: 0.06rem;
}


/* Animation */

@media (min-width: 200px) {
    .animate {
        animation-duration: 0.3s;
        -webkit-animation-duration: 0.3s;
        animation-fill-mode: both;
        -webkit-animation-fill-mode: both;
    }
}


/* Animate Slide */

@keyframes slide {
    0% {
        transform: translateY(1rem);
        opacity: 0;
    }
    100% {
        transform: translateY(0rem);
        opacity: 1;
    }
    0% {
        transform: translateY(1rem);
        opacity: 0;
    }
}

@-webkit-keyframes slide {
    0% {
        -webkit-transform: transform;
        -webkit-opacity: 0;
    }
    100% {
        -webkit-transform: translateY(0);
        -webkit-opacity: 1;
    }
    0% {
        -webkit-transform: translateY(1rem);
        -webkit-opacity: 0;
    }
}

.slide {
    -webkit-animation-name: slide;
    animation-name: slide;
}


/*----------------------------------------------*/


/* 4. SITE STRUCTURE */


/*----------------------------------------------*/


/* Preloader */

.preloader-container {
    position: fixed;
    top: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    background: var(--white-color);
    z-index: 9999999;
}

.preloader-container.active_new {
    -webkit-transform: translateY(-100vh);
    transform: translateY(-100vh);
    -webkit-transition: ease-in-out 0.5s;
    transition: ease-in-out 0.5s;
    -webkit-transition-delay: 0.3s;
    transition-delay: 0.3s;
}

.preloader-text {
    top: 45%;
    font-size: 66px;
    height: 500px;
    position: absolute;
    width: 100%;
    margin: auto;
    text-align: center;
}

.preloader-container.active_new .preloader-text {
    display: none;
}

.dark-mode .preloader-container {
    background: #343a40;
    /* Dark background for preloader in dark mode */
}

.dark-mode .preloader-text {
    color: #f8f9fa;
    /* Light text color for preloader in dark mode */
}


/* Animation for individual letters */

.preloader-text span {
    opacity: 0;
    animation: topToBottom 0.6s forwards;
    transform-origin: center;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeInLetter {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes topToBottom {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes expandWidth {
    0% {
        width: auto;
    }
    100% {
        width: 100%;
        letter-spacing: 20px;
    }
}


/* Search Bar
------------------------------------------------------------- */

.search-box {
    background: var(--gray-color);
    position: relative;
}

.close-button {
    position: absolute;
    top: 20px;
    right: 120px;
    cursor: pointer;
    z-index: 9;
}

.search-box input.search-input {
    font-size: 1.3em;
    width: 70%;
    height: 30px;
    padding: 25px;
    border-radius: 80px;
    border-color: rgb(0 0 0 / 25%);
    background: transparent;
}

.search-box svg {
    width: 22px;
    height: 22px;
    color: var(--primary-color);
}

.search-box svg.search {
    margin-left: -50px;
}


/** Search Form
--------------------------------------------------------------*/

.search-form input[type="search"].search-field {
    border: none;
    background: #f1f1f1;
    width: 100%;
    border-radius: 50px;
    padding: 10px 40px;
}

.search-form input[type="search"].search-field::focus {
    border-color: #af9aaa;
}

.search-form button {
    position: absolute;
    top: 6px;
    right: 9px;
    background: transparent;
    border: none;
}


/** Search Popup
--------------------------------------------------------------*/

.search-popup {
    background-color: #fff;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    z-index: 999999;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    transition: opacity 0.3s 0s, visibility 0s 0.3s;
}

.search-popup.is-visible {
    opacity: 1;
    visibility: visible;
    cursor: -webkit-image-set(url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath fill='%23FFF' d='M20 1l-1-1-9 9-9-9-1 1 9 9-9 9 1 1 9-9 9 9 1-1-9-9'/%3E%3C/svg%3E") 1x, url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath fill='%23000' d='M20 1l-1-1-9 9-9-9-1 1 9 9-9 9 1 1 9-9 9 9 1-1-9-9'/%3E%3C/svg%3E") 2x), pointer;
    cursor: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath fill='%23000' d='M20 1l-1-1-9 9-9-9-1 1 9 9-9 9 1 1 9-9 9 9 1-1-9-9'/%3E%3C/svg%3E"), pointer;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
    transition: opacity 0.3s 0s, visibility 0s 0s;
}

.search-popup-container {
    background-color: transparent;
    position: relative;
    top: 50%;
    margin: 0 auto;
    padding: 0;
    width: 90%;
    max-width: 800px;
    text-align: center;
    box-shadow: none;
    cursor: default;
    -webkit-transform: translateY(-40px);
    transform: translateY(-40px);
    -webkit-backface-visibility: hidden;
    -webkit-transition-property: -webkit-transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
}

.is-visible .search-popup-container {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.search-popup-form {
    position: relative;
    margin: 0 0 3em 0;
}

.search-popup-form .form-control {
    padding: 0 0 .375em 0;
    font-size: 2em;
}

.search-popup-form #search-popup-submit {
    display: none;
}

.search-popup .search-popup-close {
    display: block;
    position: absolute;
    top: 2em;
    right: 2em;
    margin: -0.5em;
    padding: 0.5em;
    line-height: 0;
}

.search-popup .search-popup-close:hover {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.search-popup .search-popup-close i {
    display: block;
    position: relative;
    width: 1em;
    height: 1em;
    fill: rgba(0, 0, 0, 0.5);
}

.search-popup .search-popup-close:hover i {
    fill: rgba(0, 0, 0, 1);
}

.search-popup .cat-list-title {
    margin-top: 40px;
    margin-bottom: 10px;
    font-size: 0.6em;
    font-weight: normal;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.search-popup .cat-list {
    margin: 0;
    list-style-type: none;
}

.search-popup .cat-list-item {
    display: inline-block;
    margin-bottom: 0;
    letter-spacing: 0.015em;
    font-size: 2em;
}

.search-popup .cat-list-item a {
    position: relative;
}

.search-popup .cat-list-item a::after {
    background: none repeat scroll 0 0 #fff;
    content: "";
    height: 1px;
    border-bottom: 1px solid #ff9697;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 100%;
    width: 100%;
    -webkit-transition: height 0.3s, opacity 0.3s, -webkit-transform 0.3s;
    transition: height 0.3s, opacity 0.3s, transform 0.3s;
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
}

.search-popup .cat-list-item a:hover::after {
    height: 1px;
    opacity: 1;
    -webkit-transform: translateY(2px);
    transform: translateY(2px);
}

.search-popup .cat-list-item::after {
    content: "/";
    padding: 0 5px;
    line-height: 1;
    color: rgba(0, 0, 0, 0.5);
    vertical-align: text-top;
}

.search-popup .cat-list-item:last-child::after {
    display: none;
}

@media only screen and (max-width: 991px) {
    .search-popup .cat-list-item,
    .search-popup-form .form-control {
        font-size: 1.425em;
    }
}

@media only screen and (max-width: 767px) {
    .search-popup .search-popup-close {
        top: 1em;
        right: 1em;
    }
}

@media only screen and (max-width: 575px) {
    .search-popup .cat-list-item,
    .search-popup-form .form-control {
        font-size: 1.125em;
    }
    .search-popup .search-popup-close {
        top: 1em;
        right: 1em;
    }
}

.search-popup input[type="search"] {
    font-size: 24px;
    height: 60px;
    padding: 26px;
}

.search-popup .search-form button {
    top: 12px;
    right: 15px;
}

.search-popup .search-form button svg {
    height: 28px;
    width: 28px;
}


/* nav tabs */

.nav-tabs .nav-link {
    color: var(--black-color);
}

:focus-visible {
    outline: none;
}


/* 4.1 Header
/*----------------------------------------------*/

.site-header {
    width: 100%;
    z-index: 10;
    transition: background 0.3s ease-out;
}

.navbar-toggler svg.navbar-icon {
    width: 50px;
    height: 50px;
}

.navbar-nav .nav-item a.nav-link {
    font-size: 0.7619em;
    font-weight: 300;
    letter-spacing: 0.09em;
    color: var(--black-color);
}

.navbar-nav .nav-item a.nav-link.active,
.navbar-nav .nav-item a.nav-link:focus,
.navbar-nav .nav-item a.nav-link:hover {
    color: var(--primary-color);
}

.modal {
    --bs-modal-zindex: 99999;
}

.modal-dialog {
    max-width: 800px;
    margin: auto;
    height: 100vh;
    display: flex;
    align-items: center;
}

.dropdown-menu {
    --bs-dropdown-link-active-color: var(--primary-color);
    --bs-dropdown-link-active-bg: #F5F5F5;
    --bs-dropdown-zindex: 99999;
    right: 0;
    left: auto;
    text-align: right;
}

.dropdown-item.active,
.dropdown-item:active {
    color: var(--bs-dropdown-link-active-color);
    text-decoration: none;
}

.cart-dropdown .dropdown-menu,
.wishlist-dropdown .dropdown-menu {
    min-width: 21rem;
}

.cart-dropdown.dropdown a::after,
.wishlist-dropdown.dropdown a::after {
    display: none;
}

@media only screen and (max-width: 500px) {
    .cart-dropdown .dropdown-menu,
    .wishlist-dropdown .dropdown-menu {
        min-width: fit-content;
    }
}


/*------------ Offcanvas -------------- */

#header-nav .navbar-toggler:focus {
    box-shadow: none;
}

#header-nav .offcanvas {
    transition: 0.5s ease-in-out;
}

#header-nav .offcanvas.show {
    z-index: 99999;
}

#header-nav .offcanvas-end {
    width: 500px;
}


/*------------ Top User Icons -----------*/

.site-header .user-items svg {
    width: 27px;
    height: 27px;
    cursor: pointer;
}

@media only screen and (max-width: 991px) {
    #navbar .user-items {
        display: none;
    }
}


/* 4.2 Billboard
/*----------------------------------------------*/


/*------------Swiper Arrow -----------*/

.swiper-next,
.swiper-prev {
    color: var(--black-color);
    z-index: 9999;
}

.swiper-button-disabled {
    color: var(--gray-color-500);
}

@media only screen and (max-width: 765px) {
    section#billboard {
        height: 100vh !important;
    }
}


/* 4.3 Icon Box - Company Services
/*----------------------------------------------*/

.icon-box .icon-box-icon svg {
    width: 33px;
    height: 33px;
    fill: var(--primary-color);
}

@media only screen and (max-width: 991px) {
    #company-services .icon-box {
        flex-wrap: wrap;
    }
}


/* 4.4 Product
/*----------------------------------------------*/

.card-concern {
    text-align: center;
    bottom: 90px;
    display: flex;
    justify-content: center;
    cursor: pointer;
    text-transform: uppercase;
    transition: all 0.3s ease-out;
    opacity: 0;
}

.card-concern .btn {
    padding: 1.125rem;
}

.card-concern svg {
    width: 27px;
    height: 27px;
    cursor: pointer;
}

.card:hover .card-concern {
    bottom: 110px;
    opacity: 1;
}

.store-card:hover {
    transform: translateY(-10px) scale(1.05);
    background-color: #ffeb3b;
    /* Yellow color on hover */
    color: #06162b;
}


/* 4.5 Items Listings
/*----------------------------------------------*/

#items-listing img {
    max-width: 80px;
    max-height: 90px;
}


/*------------Swiper Pagination -----------*/

.product-store .swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
    bottom: 35px;
}

.swiper-pagination span.swiper-pagination-bullet {
    width: 15px;
    height: 15px;
}


/* 4.6 Testimonial
/*----------------------------------------------*/

.rating svg.star {
    width: 16px;
    height: 16px;
}


/* 4.7 Brands
/*----------------------------------------------*/

#brands .brand-images img {
    filter: grayscale(100%);
    opacity: 0.5;
    transition: 0.3s ease-in-out;
}

#brands .brand-images a.brand:hover img {
    filter: grayscale(0%);
    opacity: 1;
}


/* 4.8 Instagram
/*----------------------------------------------*/

.instagram-item .icon-overlay {
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
    height: auto;
    color: var(--light-color);
    opacity: 0;
    transition: 0.9s ease-out;
}

.instagram-item:hover .icon-overlay {
    opacity: 1;
}


/* 4.9 Footer
/*----------------------------------------------*/

#footer .menu-list .menu-item a {
    font-size: 0.7619em;
    font-weight: 300;
    letter-spacing: 0.09em;
    color: var(--black-color);
}

#footer .menu-list .menu-item a:hover {
    color: var(--primary-color);
}

.social-links svg {
    width: 20px;
    height: 20px;
    transition: 0.3s ease-in-out;
    color: var(--light-gray-color);
}

.social-links svg:hover {
    color: var(--primary-color);
}

social-links li {
    padding-right: 30px;
}


/*----------------------------------------------*/


/* 5. SITE STRUCTURE */


/*----------------------------------------------*/


/* ----------- Search Bar -----------*/

select#input-sort {
    border: none;
    padding: 0;
    text-align: right;
    cursor: pointer;
}


/* 5.1 Single Product Page
/*----------------------------------------------*/

.thumb-swiper .swiper-slide {
    height: auto;
}

.swiper-slide-thumb-active img {
    border: var(--bs-border-width) var(--bs-border-style) var(--primary-color) !important;
}

.product-info del {
    color: var(--gray-color-500);
}


/*---- Product Tabs ----------*/

.nav-tabs .nav-link {
    letter-spacing: 0.06em;
}

.nav-tabs .nav-link {
    background: none;
    border: none;
}

.nav-tabs button.nav-link.active {
    color: var(--primary-color);
    background: none;
}

.nav-tabs button.nav-link:hover {
    color: var(--primary-color);
}

@media screen and (max-width: 991px) {
    .product-tabs .review-item {
        width: 100%;
        flex-wrap: wrap;
    }
    .product-tabs .review-item .image-holder {
        margin-bottom: 10px;
    }
}


/* Reverse margins and paddings for RTL */

.me-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
}

.ps-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

.pe-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}


/* Fix swiper direction */

.swiper-button-next {
    left: 10px;
    right: auto;
}

.swiper-button-prev {
    right: 10px;
    left: auto;
}


/* Fix dropdown menus */

.dropdown-menu {
    text-align: right;
}


/* Fix icons alignment */

.icon-box {
    flex-direction: row-reverse;
}


/* Fix search popup */

.search-popup .search-form {
    direction: rtl;
}


/* تحسين دعم RTL */

.rtl {
    direction: rtl;
    text-align: right;
}


/* تصحيح محاذاة العناصر في RTL */

.navbar-nav {
    padding-right: 0;
}


/* تصحيح اتجاه السلايدر */

.swiper-container-rtl .swiper-button-next {
    left: 10px;
    right: auto;
}

.swiper-container-rtl .swiper-button-prev {
    right: 10px;
    left: auto;
}


/* تصحيح اتجاه الأيقونات */

[dir="rtl"] .icon-box {
    flex-direction: row-reverse;
}


/* تعديل أزرار التنقل في السلايدر للـ RTL */

.main-slider-button-next {
    left: 20px !important;
    right: auto !important;
    transform: rotate(180deg);
}

.main-slider-button-prev {
    right: 20px !important;
    left: auto !important;
    transform: rotate(180deg);
}


/* تصحيح محاذاة النص في السلايدر */

.swiper-wrapper {
    direction: rtl;
}


/* تعديل الحركة للسلايدر في RTL */

.swiper-container-rtl .swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
    transform: rotate(180deg);
}


/* تعديل تنسيق أزرار التنقل */

.main-slider-button-next,
.main-slider-button-prev {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    cursor: pointer;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.main-slider-button-next {
    left: 20px;
}

.main-slider-button-prev {
    right: 20px;
}


/* تدوير السهام للاتجاه الصحيح */

.main-slider-button-next svg,
.main-slider-button-prev svg {
    width: 24px;
    height: 24px;
}

.main-slider-button-next svg {
    transform: scaleX(-1);
}

.main-slider-button-prev svg {
    transform: scaleX(-1);
}


/* تحسين التفاعل */

.main-slider-button-next:hover,
.main-slider-button-prev:hover {
    background: var(--primary-color);
    color: white;
}


/* Remove moon and sun icons from dark mode toggle switch */

.toggle-switch label .icon {
    display: none;
}