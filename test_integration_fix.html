<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التكامل بين الملفات الثلاثة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success {
            background-color: #28a745;
        }
        
        .status-warning {
            background-color: #ffc107;
        }
        
        .status-error {
            background-color: #dc3545;
        }
        
        .test-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .barcode-input {
            font-family: monospace;
            font-size: 16px;
            text-align: center;
        }
        
        .test-button {
            margin: 5px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs"></i> اختبار التكامل بين الملفات الثلاثة
        </h1>

        <!-- حالة الملفات -->
        <div class="test-section">
            <h3><i class="fas fa-file-code"></i> حالة الملفات</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>add_invoice.php</h6>
                            <div id="phpStatus">
                                <span class="status-indicator status-success"></span> تم إصلاح الملف المفقود
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>add_invoice.js</h6>
                            <div id="desktopJsStatus">
                                <span class="status-indicator status-success"></span> تم توحيد إدارة addedItems
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>invoice_responsive.js</h6>
                            <div id="responsiveJsStatus">
                                <span class="status-indicator status-success"></span> تم تنظيف المتغيرات غير المستخدمة
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار الباركود المخصص -->
        <div class="test-section">
            <h3><i class="fas fa-barcode"></i> اختبار الباركود المخصص</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>الوضع العادي (سطح المكتب)</h5>
                    <input type="text" id="desktopBarcode" class="form-control barcode-input mb-2" placeholder="أدخل الباركود المخصص" value="2000001001752">
                    <button onclick="testDesktopBarcode()" class="btn btn-primary test-button">
                        اختبار سطح المكتب
                    </button>
                    <div id="desktopResult" class="mt-2"></div>
                </div>
                <div class="col-md-6">
                    <h5>الوضع المتجاوب</h5>
                    <input type="text" id="responsiveBarcode" class="form-control barcode-input mb-2" placeholder="أدخل الباركود المخصص" value="2000001001752">
                    <button onclick="testResponsiveBarcode()" class="btn btn-success test-button">
                        اختبار المتجاوب
                    </button>
                    <div id="responsiveResult" class="mt-2"></div>
                </div>
            </div>
        </div>

        <!-- التحكم في الوضع -->
        <div class="test-section">
            <h3><i class="fas fa-mobile-alt"></i> التحكم في الوضع</h3>
            <div class="btn-group" role="group">
                <button onclick="setDesktopMode()" class="btn btn-outline-primary test-button">
                    وضع سطح المكتب
                </button>
                <button onclick="setResponsiveMode()" class="btn btn-outline-success test-button">
                    الوضع المتجاوب
                </button>
            </div>
            <div id="modeStatus" class="mt-2">
                <span class="badge bg-info">الوضع الحالي: سطح المكتب</span>
            </div>
        </div>

        <!-- اختبار التزامن -->
        <div class="test-section">
            <h3><i class="fas fa-sync"></i> اختبار تزامن البيانات</h3>
            <button onclick="testDataSync()" class="btn btn-info test-button">
                اختبار تزامن window.addedItems
            </button>
            <button onclick="clearTestData()" class="btn btn-warning test-button">
                مسح بيانات الاختبار
            </button>
            <div id="syncResult" class="mt-2"></div>
        </div>

        <!-- سجل الاختبارات -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> سجل الاختبارات</h3>
            <div id="testLog" class="test-log">جاهز للاختبار...</div>
            <button onclick="clearLog()" class="btn btn-secondary btn-sm mt-2">مسح السجل</button>
        </div>
    </div>

    <script>
        // محاكاة البيانات المطلوبة
        window.addedItems = [];
        window.barcodeItemsMap = {
            '000001': '1'
        };

        // محاكاة دالة التحقق من الوضع المتجاوب مع إمكانية التحكم اليدوي
        let forceResponsiveMode = false;
        window.isResponsiveMode = function() {
            return forceResponsiveMode || window.innerWidth <= 768;
        };

        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };

            logElement.textContent += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // محاكاة دالة معالجة الباركود المخصص - نسخة سطح المكتب
        function processCustomBarcodeDesktop(barcode) {
            if (window.isResponsiveMode && window.isResponsiveMode()) {
                log('تم تجاهل الباركود في سطح المكتب - الوضع متجاوب نشط', 'warning');
                return null;
            }

            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            log(`معالجة باركود سطح المكتب: ${barcode}`, 'info');
            log(`باركود الصنف: ${itemBarcode}, الوزن: ${quantityInKg} كيلو`, 'info');

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        // محاكاة دالة معالجة الباركود المخصص - نسخة متجاوبة
        function processCustomBarcodeResponsive(barcode) {
            if (window.isResponsiveMode && !window.isResponsiveMode()) {
                log('تم تجاهل الباركود في المتجاوب - الوضع العادي نشط', 'warning');
                return null;
            }

            if (!barcode.startsWith('2') || barcode.length !== 13) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightInGrams = parseInt(barcode.substring(7, 12));
            const quantityInKg = weightInGrams / 1000;

            log(`معالجة باركود متجاوب: ${barcode}`, 'info');
            log(`باركود الصنف: ${itemBarcode}, الوزن: ${quantityInKg} كيلو`, 'info');

            return {
                itemBarcode: itemBarcode,
                quantity: quantityInKg,
                weightInGrams: weightInGrams,
                isCustomBarcode: true
            };
        }

        // دوال التحكم في الوضع
        function setDesktopMode() {
            forceResponsiveMode = false;
            updateModeStatus();
            log('تم التبديل إلى وضع سطح المكتب', 'info');
        }

        function setResponsiveMode() {
            forceResponsiveMode = true;
            updateModeStatus();
            log('تم التبديل إلى الوضع المتجاوب', 'info');
        }

        function updateModeStatus() {
            const statusDiv = document.getElementById('modeStatus');
            const currentMode = window.isResponsiveMode() ? 'متجاوب' : 'سطح المكتب';
            const badgeClass = window.isResponsiveMode() ? 'bg-success' : 'bg-info';
            statusDiv.innerHTML = `<span class="badge ${badgeClass}">الوضع الحالي: ${currentMode}</span>`;
        }

        // اختبار الباركود في سطح المكتب
        function testDesktopBarcode() {
            const barcode = document.getElementById('desktopBarcode').value;
            const result = processCustomBarcodeDesktop(barcode);
            const resultDiv = document.getElementById('desktopResult');

            if (result) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>نجح الاختبار!</strong><br>
                        الكمية: ${result.quantity} كيلو<br>
                        باركود الصنف: ${result.itemBarcode}
                    </div>
                `;
                log(`اختبار سطح المكتب نجح - الكمية: ${result.quantity}`, 'success');
            } else {
                resultDiv.innerHTML = `<div class="alert alert-danger">فشل في معالجة الباركود</div>`;
                log('اختبار سطح المكتب فشل', 'error');
            }
        }

        // اختبار الباركود في الوضع المتجاوب
        function testResponsiveBarcode() {
            const barcode = document.getElementById('responsiveBarcode').value;
            const result = processCustomBarcodeResponsive(barcode);
            const resultDiv = document.getElementById('responsiveResult');

            if (result) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>نجح الاختبار!</strong><br>
                        الكمية: ${result.quantity} كيلو<br>
                        باركود الصنف: ${result.itemBarcode}
                    </div>
                `;
                log(`اختبار المتجاوب نجح - الكمية: ${result.quantity}`, 'success');
            } else {
                resultDiv.innerHTML = `<div class="alert alert-danger">فشل في معالجة الباركود</div>`;
                log('اختبار المتجاوب فشل', 'error');
            }
        }

        // اختبار تزامن البيانات
        function testDataSync() {
            // محاكاة إضافة صنف
            const testItem = {
                id: '1',
                name: 'جبنة تركي',
                quantity: 1.752,
                price: 280.00,
                isCustomBarcode: true
            };

            // التأكد من وجود window.addedItems
            if (!window.addedItems) {
                window.addedItems = [];
            }

            // إضافة الصنف
            window.addedItems.push(testItem);

            const resultDiv = document.getElementById('syncResult');
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>حالة window.addedItems:</strong><br>
                    عدد الأصناف: ${window.addedItems.length}<br>
                    آخر صنف: ${window.addedItems[window.addedItems.length - 1]?.name || 'لا يوجد'}
                </div>
            `;

            log(`تم اختبار التزامن - عدد الأصناف: ${window.addedItems.length}`, 'success');
        }

        // مسح بيانات الاختبار
        function clearTestData() {
            window.addedItems = [];
            document.getElementById('syncResult').innerHTML = '';
            log('تم مسح بيانات الاختبار', 'info');
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('testLog').textContent = 'تم مسح السجل...\n';
        }

        // تسجيل بداية الاختبار
        log('تم تحميل صفحة الاختبار بنجاح', 'success');
        log(`الوضع الحالي: ${window.isResponsiveMode() ? 'متجاوب' : 'سطح المكتب'}`, 'info');
        log('💡 نصيحة: استخدم أزرار التحكم في الوضع لاختبار كلا الوضعين', 'info');

        // تحديث حالة الوضع
        updateModeStatus();
    </script>
</body>

</html>