<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الباركود المخصص - سطح المكتب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح الباركود المخصص - سطح المكتب</h1>
        
        <div class="test-section">
            <h3>اختبار معالجة الباركود المخصص</h3>
            <input type="text" id="barcodeInput" class="test-input" placeholder="أدخل الباركود المخصص" value="2000001001752">
            <button onclick="testCustomBarcode()" class="test-button">اختبار الباركود</button>
            <div id="barcodeResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>محاكاة إضافة صنف بباركود مخصص</h3>
            <button onclick="simulateAddItem()" class="test-button">محاكاة إضافة الصنف</button>
            <div id="addItemResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>سجل الاختبار</h3>
            <button onclick="clearLog()" class="test-button">مسح السجل</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        // محاكاة البيانات المطلوبة
        let addedItems = [];
        let testLog = '';

        // دالة معالجة الباركود المخصص (نسخة من add_invoice.js)
        function processCustomBarcode(barcode) {
            if (barcode.length !== 13 || !barcode.startsWith('2')) {
                return null;
            }

            const itemBarcode = barcode.substring(1, 7);
            const weightStr = barcode.substring(7, 12);
            const weightInGrams = parseInt(weightStr);
            const quantity = weightInGrams / 1000;

            return {
                itemBarcode: itemBarcode,
                quantity: quantity,
                weightInGrams: weightInGrams
            };
        }

        // دالة اختبار الباركود
        function testCustomBarcode() {
            const barcode = document.getElementById('barcodeInput').value;
            const resultDiv = document.getElementById('barcodeResult');
            
            log(`اختبار الباركود: ${barcode}`);
            
            const customBarcodeData = processCustomBarcode(barcode);
            
            if (customBarcodeData) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>نجح تحليل الباركود:</strong><br>
                    باركود الصنف: ${customBarcodeData.itemBarcode}<br>
                    الوزن بالجرام: ${customBarcodeData.weightInGrams}<br>
                    الكمية بالكيلو: ${customBarcodeData.quantity.toFixed(3)}
                `;
                log(`✅ تم تحليل الباركود بنجاح - الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو`);
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>فشل في تحليل الباركود</strong>';
                log(`❌ فشل في تحليل الباركود`);
            }
            
            resultDiv.style.display = 'block';
        }

        // محاكاة إضافة صنف
        function simulateAddItem() {
            const barcode = document.getElementById('barcodeInput').value;
            const resultDiv = document.getElementById('addItemResult');
            
            log(`محاكاة إضافة صنف بالباركود: ${barcode}`);
            
            const customBarcodeData = processCustomBarcode(barcode);
            
            if (!customBarcodeData) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>باركود غير صحيح</strong>';
                resultDiv.style.display = 'block';
                return;
            }

            // بيانات الصنف التجريبية
            const itemId = customBarcodeData.itemBarcode;
            const itemName = 'جبنة تركي';
            const itemPrice = '280.00';
            
            // التحقق من وجود الصنف مسبقاً
            const existingItemIndex = addedItems.findIndex(item => item.id === itemId);
            
            if (existingItemIndex !== -1) {
                // تحديث الصنف الموجود
                addedItems[existingItemIndex].quantity = customBarcodeData.quantity;
                addedItems[existingItemIndex].isCustomBarcode = true;
                addedItems[existingItemIndex].customBarcodeData = customBarcodeData;
                log(`تم تحديث صنف موجود - الكمية الجديدة: ${customBarcodeData.quantity.toFixed(3)} كيلو`);
            } else {
                // إضافة صنف جديد بالكمية الصحيحة مباشرة
                addedItems.push({
                    id: itemId,
                    name: itemName,
                    quantity: customBarcodeData.quantity,
                    price: itemPrice,
                    isCustomBarcode: true,
                    customBarcodeData: customBarcodeData
                });
                log(`تم إضافة صنف جديد - الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو`);
            }

            // حساب الإجمالي
            const totalAmount = customBarcodeData.quantity * parseFloat(itemPrice);
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>تم إضافة الصنف بنجاح:</strong><br>
                الصنف: ${itemName}<br>
                الكمية: ${customBarcodeData.quantity.toFixed(3)} كيلو<br>
                السعر: ${itemPrice} جنيه/كيلو<br>
                <strong>الإجمالي: ${totalAmount.toFixed(2)} جنيه</strong><br>
                عدد الأصناف المضافة: ${addedItems.length}
            `;
            resultDiv.style.display = 'block';
            
            log(`✅ الإجمالي المحسوب: ${totalAmount.toFixed(2)} جنيه`);
            log(`📊 إجمالي الأصناف: ${addedItems.length}`);
        }

        // دالة تسجيل الأحداث
        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            testLog += `[${timestamp}] ${message}\n`;
            document.getElementById('testLog').textContent = testLog;
            document.getElementById('testLog').scrollTop = document.getElementById('testLog').scrollHeight;
        }

        // مسح السجل
        function clearLog() {
            testLog = '';
            document.getElementById('testLog').textContent = '';
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 تم تحميل صفحة الاختبار');
            log('📝 جاهز لاختبار الباركود المخصص');
        };
    </script>
</body>
</html>
