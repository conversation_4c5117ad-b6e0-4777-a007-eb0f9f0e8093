<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الأصناف المختارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .debug-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .debug-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-button {
            margin: 5px;
        }
        
        .element-check {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        
        .element-found {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .element-missing {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug"></i>
            تشخيص مشكلة الأصناف المختارة
        </h1>
        
        <!-- فحص العناصر -->
        <div class="debug-section">
            <h3>🔍 فحص العناصر المطلوبة</h3>
            <button onclick="checkElements()" class="btn btn-primary test-button">
                فحص العناصر
            </button>
            <div id="elementsCheck" class="mt-3"></div>
        </div>
        
        <!-- محاكاة إضافة صنف -->
        <div class="debug-section">
            <h3>🧪 محاكاة إضافة صنف</h3>
            <button onclick="simulateAddItem()" class="btn btn-success test-button">
                محاكاة إضافة صنف
            </button>
            <button onclick="simulateCustomBarcode()" class="btn btn-warning test-button">
                محاكاة باركود مخصص
            </button>
            <div id="simulationResult" class="mt-3"></div>
        </div>
        
        <!-- فحص البيانات -->
        <div class="debug-section">
            <h3>📊 فحص البيانات</h3>
            <button onclick="checkData()" class="btn btn-info test-button">
                فحص window.addedItems
            </button>
            <button onclick="checkCategories()" class="btn btn-secondary test-button">
                فحص categories
            </button>
            <div id="dataCheck" class="mt-3"></div>
        </div>
        
        <!-- سجل التشخيص -->
        <div class="debug-section">
            <h3>📝 سجل التشخيص</h3>
            <div id="debugLog" class="debug-log">جاهز للتشخيص...</div>
            <button onclick="clearLog()" class="btn btn-secondary btn-sm mt-2">مسح السجل</button>
        </div>
    </div>

    <script>
        // محاكاة البيانات المطلوبة
        window.addedItems = [];
        window.barcodeItemsMap = {
            '000001': '1'
        };
        
        // محاكاة categories
        window.categories = [
            {
                id: '1',
                name: 'منتجات الألبان',
                items: [
                    {
                        id: '1',
                        name: 'جبنة تركي',
                        price: 280.00,
                        barcode: '000001'
                    }
                ]
            }
        ];
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };
            
            logElement.textContent += `[${timestamp}] ${typeIcon[type]} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // فحص العناصر المطلوبة
        function checkElements() {
            const elementsToCheck = [
                'selectedItemsTable',
                'accountsTable',
                'total_amount_value',
                'item-count'
            ];
            
            const checkDiv = document.getElementById('elementsCheck');
            checkDiv.innerHTML = '';
            
            log('بدء فحص العناصر المطلوبة...', 'info');
            
            elementsToCheck.forEach(elementId => {
                const element = document.getElementById(elementId);
                const elementDiv = document.createElement('div');
                elementDiv.className = 'element-check';
                
                if (element) {
                    elementDiv.className += ' element-found';
                    elementDiv.innerHTML = `✅ ${elementId} - موجود`;
                    log(`العنصر ${elementId} موجود`, 'success');
                } else {
                    elementDiv.className += ' element-missing';
                    elementDiv.innerHTML = `❌ ${elementId} - مفقود`;
                    log(`العنصر ${elementId} مفقود!`, 'error');
                }
                
                checkDiv.appendChild(elementDiv);
            });
            
            // فحص إضافي للعناصر المتجاوبة
            const responsiveContainer = document.querySelector('.responsive-container');
            const elementDiv = document.createElement('div');
            elementDiv.className = 'element-check';
            
            if (responsiveContainer) {
                elementDiv.className += ' element-found';
                elementDiv.innerHTML = `✅ responsive-container - موجود`;
                log('الحاوية المتجاوبة موجودة', 'success');
            } else {
                elementDiv.className += ' element-missing';
                elementDiv.innerHTML = `❌ responsive-container - مفقود (الوضع العادي نشط)`;
                log('الحاوية المتجاوبة غير موجودة - الوضع العادي نشط', 'warning');
            }
            
            checkDiv.appendChild(elementDiv);
        }
        
        // محاكاة إضافة صنف
        function simulateAddItem() {
            log('محاكاة إضافة صنف عادي...', 'info');
            
            const testItem = {
                id: '1',
                name: 'جبنة تركي',
                price: 280.00,
                quantity: 1
            };
            
            // محاكاة دالة addItemToSelectedList
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            const resultDiv = document.getElementById('simulationResult');
            
            if (!selectedItemsTable) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ!</strong> جدول الأصناف المختارة غير موجود.<br>
                        هذا يعني أن الوضع المقسم غير نشط حالياً.
                    </div>
                `;
                log('فشل المحاكاة - جدول الأصناف المختارة غير موجود', 'error');
                return;
            }
            
            // إضافة الصنف إلى window.addedItems
            if (!window.addedItems) {
                window.addedItems = [];
            }
            
            const existingIndex = window.addedItems.findIndex(item => item.id === testItem.id);
            if (existingIndex >= 0) {
                window.addedItems[existingIndex].quantity += testItem.quantity;
                log(`تم تحديث كمية الصنف الموجود: ${testItem.name}`, 'info');
            } else {
                window.addedItems.push(testItem);
                log(`تم إضافة صنف جديد: ${testItem.name}`, 'success');
            }
            
            // محاكاة إضافة صف للجدول
            const tr = document.createElement('tr');
            tr.dataset.itemId = testItem.id;
            tr.innerHTML = `
                <td style="padding: 8px;">${testItem.name}</td>
                <td style="padding: 8px;">${testItem.price}</td>
                <td style="padding: 8px;">${testItem.quantity}</td>
                <td style="padding: 8px;">${(testItem.price * testItem.quantity).toFixed(2)}</td>
                <td style="padding: 8px;">
                    <button onclick="removeTestItem('${testItem.id}')" class="btn btn-sm btn-danger">حذف</button>
                </td>
            `;
            
            selectedItemsTable.appendChild(tr);
            
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <strong>نجح!</strong> تم إضافة الصنف بنجاح.<br>
                    الصنف: ${testItem.name}<br>
                    عدد الأصناف في window.addedItems: ${window.addedItems.length}<br>
                    عدد الصفوف في الجدول: ${selectedItemsTable.children.length}
                </div>
            `;
            
            log(`نجحت المحاكاة - تم إضافة ${testItem.name}`, 'success');
        }
        
        // محاكاة باركود مخصص
        function simulateCustomBarcode() {
            log('محاكاة باركود مخصص...', 'info');
            
            const barcode = '2000001001752'; // 1.752 كيلو من الصنف 000001
            const itemBarcode = barcode.substring(1, 7); // 000001
            const weightInGrams = parseInt(barcode.substring(7, 12)); // 01752
            const quantityInKg = weightInGrams / 1000; // 1.752
            
            log(`باركود مخصص: ${barcode}`, 'info');
            log(`باركود الصنف: ${itemBarcode}`, 'info');
            log(`الوزن: ${quantityInKg} كيلو`, 'info');
            
            // البحث عن الصنف
            const itemId = window.barcodeItemsMap[itemBarcode];
            if (!itemId) {
                log(`باركود الصنف غير معروف: ${itemBarcode}`, 'error');
                return;
            }
            
            // البحث في categories
            let foundItem = null;
            for (let category of window.categories) {
                foundItem = category.items.find(item => item.id === itemId);
                if (foundItem) break;
            }
            
            if (!foundItem) {
                log(`الصنف غير موجود في categories: ${itemId}`, 'error');
                return;
            }
            
            // إنشاء صنف مخصص
            const customItem = {
                ...foundItem,
                quantity: quantityInKg,
                isCustomBarcode: true,
                customBarcodeData: {
                    itemBarcode: itemBarcode,
                    quantity: quantityInKg,
                    weightInGrams: weightInGrams
                }
            };
            
            log(`تم إنشاء صنف مخصص: ${customItem.name} - ${quantityInKg} كيلو`, 'success');
            
            // محاكاة الإضافة
            simulateAddCustomItem(customItem);
        }
        
        function simulateAddCustomItem(item) {
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            const resultDiv = document.getElementById('simulationResult');
            
            if (!selectedItemsTable) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ!</strong> جدول الأصناف المختارة غير موجود.
                    </div>
                `;
                return;
            }
            
            // إضافة للبيانات
            if (!window.addedItems) {
                window.addedItems = [];
            }
            
            window.addedItems.push(item);
            
            // إضافة للجدول
            const tr = document.createElement('tr');
            tr.dataset.itemId = item.id;
            tr.dataset.isCustomBarcode = item.isCustomBarcode;
            tr.innerHTML = `
                <td style="padding: 8px;">${item.name} ${item.isCustomBarcode ? '(مخصص)' : ''}</td>
                <td style="padding: 8px;">${item.price}</td>
                <td style="padding: 8px;">${item.quantity.toFixed(3)}</td>
                <td style="padding: 8px;">${(item.price * item.quantity).toFixed(2)}</td>
                <td style="padding: 8px;">
                    <button onclick="removeTestItem('${item.id}')" class="btn btn-sm btn-danger">حذف</button>
                </td>
            `;
            
            selectedItemsTable.appendChild(tr);
            
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <strong>نجح!</strong> تم إضافة الصنف المخصص بنجاح.<br>
                    الصنف: ${item.name}<br>
                    الكمية: ${item.quantity.toFixed(3)} كيلو<br>
                    الإجمالي: ${(item.price * item.quantity).toFixed(2)} جنيه
                </div>
            `;
            
            log(`نجحت محاكاة الباركود المخصص - ${item.name}`, 'success');
        }
        
        // حذف صنف تجريبي
        function removeTestItem(itemId) {
            const selectedItemsTable = document.getElementById('selectedItemsTable');
            const row = selectedItemsTable.querySelector(`tr[data-item-id="${itemId}"]`);
            if (row) {
                row.remove();
                log(`تم حذف الصنف: ${itemId}`, 'info');
            }
            
            // حذف من window.addedItems
            if (window.addedItems) {
                window.addedItems = window.addedItems.filter(item => item.id !== itemId);
            }
        }
        
        // فحص البيانات
        function checkData() {
            const dataDiv = document.getElementById('dataCheck');
            
            log('فحص البيانات...', 'info');
            
            let html = '<h5>حالة البيانات:</h5>';
            
            // فحص window.addedItems
            if (window.addedItems) {
                html += `<p><strong>window.addedItems:</strong> موجود (${window.addedItems.length} عنصر)</p>`;
                log(`window.addedItems موجود - ${window.addedItems.length} عنصر`, 'success');
            } else {
                html += `<p><strong>window.addedItems:</strong> غير موجود</p>`;
                log('window.addedItems غير موجود', 'error');
            }
            
            // فحص barcodeItemsMap
            if (window.barcodeItemsMap) {
                const count = Object.keys(window.barcodeItemsMap).length;
                html += `<p><strong>barcodeItemsMap:</strong> موجود (${count} باركود)</p>`;
                log(`barcodeItemsMap موجود - ${count} باركود`, 'success');
            } else {
                html += `<p><strong>barcodeItemsMap:</strong> غير موجود</p>`;
                log('barcodeItemsMap غير موجود', 'error');
            }
            
            dataDiv.innerHTML = html;
        }
        
        // فحص categories
        function checkCategories() {
            const dataDiv = document.getElementById('dataCheck');
            
            log('فحص categories...', 'info');
            
            let html = '<h5>حالة الفئات:</h5>';
            
            if (window.categories && Array.isArray(window.categories)) {
                html += `<p><strong>categories:</strong> موجود (${window.categories.length} فئة)</p>`;
                
                let totalItems = 0;
                window.categories.forEach(category => {
                    totalItems += category.items ? category.items.length : 0;
                    html += `<p>- ${category.name}: ${category.items ? category.items.length : 0} صنف</p>`;
                });
                
                html += `<p><strong>إجمالي الأصناف:</strong> ${totalItems}</p>`;
                log(`categories موجود - ${window.categories.length} فئة، ${totalItems} صنف`, 'success');
            } else {
                html += `<p><strong>categories:</strong> غير موجود أو غير صحيح</p>`;
                log('categories غير موجود أو غير صحيح', 'error');
            }
            
            dataDiv.innerHTML = html;
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('debugLog').textContent = 'تم مسح السجل...\n';
        }
        
        // تسجيل بداية التشخيص
        log('تم تحميل أداة التشخيص بنجاح', 'success');
        log('💡 ابدأ بفحص العناصر لتحديد المشكلة', 'info');
    </script>
</body>
</html>
